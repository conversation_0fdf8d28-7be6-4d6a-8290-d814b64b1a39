/**
 * @file test_protocol_identifier.c
 * @brief 协议识别功能测试用例
 * <AUTHOR> Agent
 * @date 2025-07-31
 */

#include "protocol_identifier.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <assert.h>

/* 跨平台字节序转换 */
#ifdef _WIN32
    #include <winsock2.h>
    #pragma comment(lib, "ws2_32.lib")
#else
    #include <arpa/inet.h>
#endif

/* 测试用例计数器 */
static int test_count = 0;
static int test_passed = 0;

#define TEST_ASSERT(condition, description) do { \
    test_count++; \
    if (condition) { \
        test_passed++; \
        printf("✓ Test %d PASSED: %s\n", test_count, description); \
    } else { \
        printf("✗ Test %d FAILED: %s\n", test_count, description); \
    } \
} while(0)

/**
 * @brief 创建测试用的ASN.1数据包
 */
void create_test_asn1_packet(unsigned char* buffer, size_t* len) {
    /* 创建一个简单的ASN.1 SEQUENCE */
    /* SEQUENCE { INTEGER 123, OCTET STRING "test" } */
    unsigned char asn1_data[] = {
        0x30, 0x09,                    /* SEQUENCE, length 9 */
        0x02, 0x01, 0x7B,              /* INTEGER 123 */
        0x04, 0x04, 't', 'e', 's', 't' /* OCTET STRING "test" */
    };
    
    memcpy(buffer, asn1_data, sizeof(asn1_data));
    *len = sizeof(asn1_data);
}

/**
 * @brief 创建测试用的HSM数据包
 */
void create_test_hsm_packet(unsigned char* buffer, size_t* len) {
    HSM_REQ_HEAD header;
    const char* payload = "Hello HSM Protocol";
    size_t payload_len = strlen(payload);
    
    /* 填充包头 */
    header.tlen = htonl(sizeof(HSM_REQ_HEAD) + payload_len);
    header.checksum = htonl(calculate_simple_checksum((unsigned char*)payload, payload_len));
    header.action = htons(1001);
    header.version = htons(0x0101);  /* 主版本1，次版本1 */
    header.reserve = htonl(0);
    
    /* 组装数据包 */
    memcpy(buffer, &header, sizeof(HSM_REQ_HEAD));
    memcpy(buffer + sizeof(HSM_REQ_HEAD), payload, payload_len);
    *len = sizeof(HSM_REQ_HEAD) + payload_len;
}

/**
 * @brief 测试ASN.1协议识别
 */
void test_asn1_identification() {
    printf("\n=== Testing ASN.1 Protocol Identification ===\n");
    
    unsigned char buffer[256];
    size_t len;
    
    /* 测试1: 正常的ASN.1 SEQUENCE */
    create_test_asn1_packet(buffer, &len);
    int result = identify_protocol(buffer, len);
    TEST_ASSERT(result == PROTOCOL_ASN1, "Valid ASN.1 SEQUENCE identification");
    
    /* 测试2: ASN.1 INTEGER */
    unsigned char asn1_int[] = {0x02, 0x01, 0x7B};  /* INTEGER 123 */
    result = identify_protocol(asn1_int, sizeof(asn1_int));
    TEST_ASSERT(result == PROTOCOL_ASN1, "ASN.1 INTEGER identification");
    
    /* 测试3: ASN.1 OCTET STRING */
    unsigned char asn1_octet[] = {0x04, 0x04, 't', 'e', 's', 't'};
    result = identify_protocol(asn1_octet, sizeof(asn1_octet));
    TEST_ASSERT(result == PROTOCOL_ASN1, "ASN.1 OCTET STRING identification");
    
    /* 测试4: 长格式长度的ASN.1 */
    unsigned char asn1_long[] = {0x30, 0x81, 0x05, 0x02, 0x01, 0x7B, 0x05, 0x00};
    result = identify_protocol(asn1_long, sizeof(asn1_long));
    TEST_ASSERT(result == PROTOCOL_ASN1, "ASN.1 with long form length");
    
    /* 测试5: 无效的ASN.1标签 */
    unsigned char invalid_tag[] = {0xFF, 0x05, 0x01, 0x02, 0x03};
    result = identify_protocol(invalid_tag, sizeof(invalid_tag));
    TEST_ASSERT(result != PROTOCOL_ASN1, "Invalid ASN.1 tag rejection");
}

/**
 * @brief 测试HSM协议识别
 */
void test_hsm_identification() {
    printf("\n=== Testing HSM Protocol Identification ===\n");
    
    unsigned char buffer[256];
    size_t len;
    
    /* 测试1: 正常的HSM数据包 */
    create_test_hsm_packet(buffer, &len);
    //打印buffer
    for(int i = 0; i < len; i++) {
        printf("%02X ", buffer[i]);
    }
    printf("\n");
    int result = identify_protocol(buffer, len);
    TEST_ASSERT(result == PROTOCOL_CUSTOM, "Valid HSM packet identification");
    
    /* 测试2: 只有包头的HSM数据包 */
    HSM_REQ_HEAD header;
    header.tlen = htonl(sizeof(HSM_REQ_HEAD));
    header.checksum = htonl(0);
    header.action = htons(2001);
    header.version = htons(0x0200);
    header.reserve = htonl(0);
    
    result = identify_protocol((unsigned char*)&header, sizeof(header));
    TEST_ASSERT(result == PROTOCOL_CUSTOM, "HSM header-only packet identification");
    
    /* 测试3: 无效的tlen字段 */
    header.tlen = htonl(5);  /* 小于最小包头大小 */
    result = identify_protocol((unsigned char*)&header, sizeof(header));
    TEST_ASSERT(result != PROTOCOL_CUSTOM, "Invalid tlen field rejection");
    
    /* 测试4: 无效的action字段 */
    header.tlen = htonl(sizeof(HSM_REQ_HEAD));
    header.action = htons(99999);  /* 超出合理范围 */
    result = identify_protocol((unsigned char*)&header, sizeof(header));
    TEST_ASSERT(result != PROTOCOL_CUSTOM, "Invalid action field rejection");
    
    /* 测试5: 无效的版本号 */
    header.action = htons(1001);
    header.version = htons(0xFF00);  /* 主版本号过大 */
    result = identify_protocol((unsigned char*)&header, sizeof(header));
    TEST_ASSERT(result != PROTOCOL_CUSTOM, "Invalid version field rejection");
}

/**
 * @brief 测试边界情况
 */
void test_edge_cases() {
    printf("\n=== Testing Edge Cases ===\n");
    
    /* 测试1: 空数据 */
    int result = identify_protocol(NULL, 0);
    TEST_ASSERT(result == PROTOCOL_UNKNOWN, "NULL data handling");
    
    /* 测试2: 数据长度为0 */
    unsigned char dummy = 0;
    result = identify_protocol(&dummy, 0);
    TEST_ASSERT(result == PROTOCOL_UNKNOWN, "Zero length data handling");
    
    /* 测试3: 数据长度过小 */
    unsigned char small_data[] = {0x30};
    result = identify_protocol(small_data, 1);
    TEST_ASSERT(result == PROTOCOL_UNKNOWN, "Insufficient data length handling");
    
    /* 测试4: 随机数据 */
    unsigned char random_data[] = {0xAB, 0xCD, 0xEF, 0x12, 0x34, 0x56, 0x78, 0x90,
                                  0x11, 0x22, 0x33, 0x44, 0x55, 0x66, 0x77, 0x88};
    result = identify_protocol(random_data, sizeof(random_data));
    TEST_ASSERT(result == PROTOCOL_UNKNOWN, "Random data rejection");
    
    /* 测试5: 部分HSM数据包 */
    unsigned char buffer[256];
    size_t len;
    create_test_hsm_packet(buffer, &len);
    result = identify_protocol(buffer, sizeof(HSM_REQ_HEAD) - 1);
    TEST_ASSERT(result == PROTOCOL_UNKNOWN, "Partial HSM packet rejection");
}

/**
 * @brief 测试性能（简单测试）
 */
void test_performance() {
    printf("\n=== Testing Performance ===\n");
    
    unsigned char asn1_buffer[256];
    unsigned char hsm_buffer[256];
    size_t asn1_len, hsm_len;
    
    create_test_asn1_packet(asn1_buffer, &asn1_len);
    create_test_hsm_packet(hsm_buffer, &hsm_len);
    
    /* 简单的性能测试 */
    const int iterations = 10000;
    
    printf("Running %d iterations for performance test...\n", iterations);
    
    for (int i = 0; i < iterations; i++) {
        identify_protocol(asn1_buffer, asn1_len);
        identify_protocol(hsm_buffer, hsm_len);
    }
    
    printf("Performance test completed successfully\n");
}

/**
 * @brief 演示使用示例
 */
void demo_usage() {
    printf("\n=== Usage Demo ===\n");

    unsigned char asn1_buffer[256];
    unsigned char hsm_buffer[256];
    size_t asn1_len, hsm_len;

    create_test_asn1_packet(asn1_buffer, &asn1_len);
    create_test_hsm_packet(hsm_buffer, &hsm_len);

    printf("Demo 1: ASN.1 packet identification\n");
    int result = identify_protocol(asn1_buffer, asn1_len);
    printf("  Result: %s (%d)\n", get_protocol_name(result), result);

    printf("Demo 2: HSM packet identification\n");
    result = identify_protocol(hsm_buffer, hsm_len);
    printf("  Result: %s (%d)\n", get_protocol_name(result), result);

    printf("Demo 3: Unknown packet identification\n");
    unsigned char unknown[] = {0xFF, 0xFF, 0xFF, 0xFF};
    result = identify_protocol(unknown, sizeof(unknown));
    printf("  Result: %s (%d)\n", get_protocol_name(result), result);
}

/**
 * @brief 主测试函数
 */
int main() {
    printf("Protocol Identifier Test Suite\n");
    printf("==============================\n");

    test_asn1_identification();
    test_hsm_identification();
    test_edge_cases();
    test_performance();
    demo_usage();

    printf("\n=== Test Summary ===\n");
    printf("Total tests: %d\n", test_count);
    printf("Passed: %d\n", test_passed);
    printf("Failed: %d\n", test_count - test_passed);
    printf("Success rate: %.1f%%\n",
           test_count > 0 ? (100.0 * test_passed / test_count) : 0.0);

    return (test_passed == test_count) ? 0 : 1;
}
