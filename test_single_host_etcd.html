<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单宿主机etcd集群 - OVS VLAN网络架构</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; line-height: 1.6; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .container { max-width: 1400px; margin: 0 auto; background: white; padding: 30px; border-radius: 15px; box-shadow: 0 15px 35px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .header h1 { color: #2c3e50; margin-bottom: 10px; font-size: 2.8em; text-shadow: 2px 2px 4px rgba(0,0,0,0.1); }
        .header .subtitle { color: #7f8c8d; font-size: 1.3em; font-weight: 300; }
        .section { margin: 25px 0; padding: 25px; border-left: 5px solid #667eea; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); border-radius: 10px; }
        .highlight { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 15px; margin: 20px 0; box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3); }
        .feature-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 25px; margin: 25px 0; }
        .feature-card { background: white; padding: 25px; border-radius: 15px; box-shadow: 0 8px 25px rgba(0,0,0,0.1); border-top: 5px solid #667eea; transition: transform 0.3s; }
        .feature-card:hover { transform: translateY(-5px); }
        .feature-card h3 { color: #2c3e50; margin-bottom: 15px; font-size: 1.3em; }
        .btn { display: inline-block; padding: 15px 30px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; text-decoration: none; border-radius: 30px; margin: 10px 10px 10px 0; transition: all 0.3s; font-weight: 500; }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4); }
        .comparison-table { width: 100%; border-collapse: collapse; margin: 20px 0; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 15px rgba(0,0,0,0.1); }
        .comparison-table th, .comparison-table td { padding: 15px; text-align: left; border-bottom: 1px solid #e9ecef; }
        .comparison-table th { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; font-weight: 600; }
        .comparison-table tr:nth-child(even) { background: #f8f9fa; }
        .comparison-table tr:hover { background: #e3f2fd; }
        .tech-stack { background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); padding: 20px; border-radius: 10px; margin: 15px 0; border-left: 4px solid #667eea; }
        .code-block { background: #2c3e50; color: #ecf0f1; padding: 20px; border-radius: 10px; font-family: 'Courier New', monospace; overflow-x: auto; margin: 15px 0; box-shadow: inset 0 2px 10px rgba(0,0,0,0.3); }
        .architecture-flow { display: flex; justify-content: space-between; align-items: center; margin: 25px 0; flex-wrap: wrap; }
        .flow-item { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 12px 25px; border-radius: 25px; margin: 8px; font-weight: 500; box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3); }
        .arrow { font-size: 1.8em; color: #667eea; font-weight: bold; }
        .network-diagram { background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0; border: 2px dashed #667eea; }
        .vlan-info { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin: 20px 0; }
        .vlan-card { padding: 15px; border-radius: 8px; text-align: center; color: white; font-weight: 500; }
        .vlan-100 { background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%); }
        .vlan-200 { background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%); }
        .vlan-300 { background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%); }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🖥️ 单宿主机etcd集群</h1>
            <div class="subtitle">OVS VLAN网络架构 - Trunk vs Access模式详细对比</div>
        </div>
        
        <div class="highlight">
            <h2>🎯 架构核心特性</h2>
            <div class="architecture-flow">
                <div class="flow-item">单台宿主机</div>
                <span class="arrow">→</span>
                <div class="flow-item">3个etcd虚拟机</div>
                <span class="arrow">→</span>
                <div class="flow-item">OVS虚拟交换</div>
                <span class="arrow">→</span>
                <div class="flow-item">VLAN网络隔离</div>
                <span class="arrow">→</span>
                <div class="flow-item">服务发现集群</div>
            </div>
        </div>

        <div class="section">
            <h2>📊 快速访问</h2>
            <a href="https://app.diagrams.net/?lightbox=1&highlight=0000ff&edit=_blank&layers=1&nav=1&title=OVS_VLAN_Single_Host_etcd_Cluster.drawio#R" target="_blank" class="btn">
                🎯 在 Draw.io 中打开架构图
            </a>
            <a href="OVS_VLAN_Single_Host_etcd_Cluster.drawio" download class="btn">
                💾 下载架构图文件
            </a>
            <a href="test_cluster_architecture.html" class="btn">
                🔄 查看多宿主机集群对比
            </a>
        </div>

        <div class="section">
            <h2>🌐 简化VLAN网络规划</h2>
            <div class="highlight">
                <h3>🔄 重要更新：网络配置简化</h3>
                <p><strong>新架构特点：</strong>只使用2个网口，集群内部通信使用业务网络，VIP负载均衡器部署在etcd-node-1上</p>
            </div>
            <div class="vlan-info">
                <div class="vlan-card vlan-100">
                    <h3>VLAN 100 - 业务网络 (统一)</h3>
                    <p>*************/24</p>
                    <p>✅ etcd客户端访问 (端口2379)</p>
                    <p>✅ etcd集群内部通信 (端口2380)</p>
                    <p>✅ HAProxy负载均衡</p>
                </div>
                <div class="vlan-card vlan-300">
                    <h3>VLAN 300 - 管理网络</h3>
                    <p>192.168.300.0/24</p>
                    <p>✅ SSH管理访问 (端口22)</p>
                    <p>✅ 监控数据收集 (端口9100)</p>
                    <p>✅ 独立安全管理</p>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🔧 Trunk vs Access模式详细对比</h2>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>对比维度</th>
                        <th>Trunk模式</th>
                        <th>Access模式</th>
                        <th>推荐场景</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>VM网络接口</strong></td>
                        <td>单网卡多VLAN<br>(eth0.100, eth0.200, eth0.300)</td>
                        <td>多网卡单VLAN<br>(eth0, eth1, eth2)</td>
                        <td>Trunk: 接口有限<br>Access: 性能优先</td>
                    </tr>
                    <tr>
                        <td><strong>OVS配置复杂度</strong></td>
                        <td>单网桥 br0<br>端口设置trunk</td>
                        <td>多网桥<br>br-business, br-cluster, br-mgmt</td>
                        <td>Trunk: 配置简单<br>Access: 隔离彻底</td>
                    </tr>
                    <tr>
                        <td><strong>VM配置复杂度</strong></td>
                        <td>需要配置VLAN子接口<br>ip link add eth0.100</td>
                        <td>直接配置物理接口<br>ip addr add</td>
                        <td>Access: VM配置更简单</td>
                    </tr>
                    <tr>
                        <td><strong>网络性能</strong></td>
                        <td>VLAN标签处理开销<br>共享物理带宽</td>
                        <td>无标签开销<br>独立网络路径</td>
                        <td>Access: 性能更优</td>
                    </tr>
                    <tr>
                        <td><strong>故障排查</strong></td>
                        <td>需要理解VLAN标签<br>tcpdump -i eth0 vlan</td>
                        <td>网络路径清晰<br>tcpdump -i eth0</td>
                        <td>Access: 更易排查</td>
                    </tr>
                    <tr>
                        <td><strong>安全隔离</strong></td>
                        <td>逻辑隔离<br>共享物理接口</td>
                        <td>物理隔离<br>独立网络路径</td>
                        <td>Access: 安全性更高</td>
                    </tr>
                    <tr>
                        <td><strong>扩展性</strong></td>
                        <td>易于添加新VLAN<br>修改trunk配置</td>
                        <td>需要新增网桥<br>添加物理接口</td>
                        <td>Trunk: 扩展更灵活</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>🛠️ 技术实现详情</h2>
            <div class="feature-grid">
                <div class="feature-card">
                    <h3>🔧 Trunk模式配置</h3>
                    <div class="tech-stack">
                        <strong>OVS网桥配置:</strong>
                        <div class="code-block">
# 创建单个网桥
ovs-vsctl add-br br0
ovs-vsctl add-port br0 eth0
ovs-vsctl set port eth0 trunk=100,200,300

# VM端口配置
ovs-vsctl add-port br0 vnet0 trunk=100,200,300
ovs-vsctl add-port br0 vnet1 trunk=100,200,300
ovs-vsctl add-port br0 vnet2 trunk=100,200,300
                        </div>
                    </div>
                    <div class="tech-stack">
                        <strong>VM内VLAN接口配置:</strong>
                        <div class="code-block">
# 创建VLAN子接口
ip link add link eth0 name eth0.100 type vlan id 100
ip link add link eth0 name eth0.200 type vlan id 200
ip link add link eth0 name eth0.300 type vlan id 300

# 配置IP地址
ip addr add **************/24 dev eth0.100
ip addr add **************/24 dev eth0.200
ip addr add 192.168.300.11/24 dev eth0.300
                        </div>
                    </div>
                </div>
                
                <div class="feature-card">
                    <h3>🔌 Access模式配置</h3>
                    <div class="tech-stack">
                        <strong>OVS多网桥配置:</strong>
                        <div class="code-block">
# 创建多个专用网桥
ovs-vsctl add-br br-business
ovs-vsctl add-br br-cluster  
ovs-vsctl add-br br-mgmt

# 设置VLAN标签
ovs-vsctl set port br-business tag=100
ovs-vsctl set port br-cluster tag=200
ovs-vsctl set port br-mgmt tag=300

# VM端口分配
ovs-vsctl add-port br-business vnet0-eth0
ovs-vsctl add-port br-cluster vnet0-eth1
ovs-vsctl add-port br-mgmt vnet0-eth2
                        </div>
                    </div>
                    <div class="tech-stack">
                        <strong>VM多网卡配置:</strong>
                        <div class="code-block">
# 直接配置物理接口
ip addr add **************/24 dev eth0
ip addr add **************/24 dev eth1
ip addr add 192.168.300.11/24 dev eth2

# 启用接口
ip link set eth0 up
ip link set eth1 up
ip link set eth2 up
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🌐 VIP负载均衡配置</h2>
            <div class="highlight">
                <h3>💡 重要说明：集群模式下的VIP配置</h3>
                <p>与传统主备模式不同，集群模式使用<strong>负载均衡器</strong>提供VIP服务，而非VRRP协议。这种方式具有更好的性能和可用性。</p>
                <p><strong>关键问题：负载均衡器部署在哪里？</strong></p>
            </div>

            <div class="section">
                <h3>🏗️ 负载均衡器部署方案</h3>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h3>✅ 当前方案: etcd-node-1集成部署 (已采用)</h3>
                        <div class="architecture-flow">
                            <div class="flow-item" style="background: #e74c3c;">etcd-node-1 + HAProxy</div>
                            <div class="flow-item">etcd-node-2</div>
                            <div class="flow-item">etcd-node-3</div>
                        </div>
                        <ul>
                            <li><strong>部署位置</strong>: etcd-node-1上集成HAProxy</li>
                            <li><strong>节点IP</strong>: **************</li>
                            <li><strong>VIP地址</strong>: *************** (绑定到eth0.100)</li>
                            <li><strong>网络配置</strong>: 双网口 (业务+管理)</li>
                            <li><strong>优势</strong>: 节省资源、配置简化、适合小规模</li>
                            <li><strong>注意</strong>: node-1故障影响VIP，需要监控</li>
                        </ul>
                    </div>

                    <div class="feature-card">
                        <h3>🔧 HAProxy配置详情</h3>
                        <div class="code-block">
# HAProxy后端配置 (在etcd-node-1上)
backend etcd_backend
    balance roundrobin
    option tcp-check
    server etcd1 127.0.0.1:2379 check      # 本机
    server etcd2 **************:2379 check # node-2
    server etcd3 **************:2379 check # node-3

# VIP绑定
ip addr add ***************/24 dev eth0.100
                        </div>
                    </div>

                    <div class="feature-card">
                        <h3>📊 架构简化对比</h3>
                        <table class="comparison-table">
                            <tr>
                                <th>配置项</th>
                                <th>原架构</th>
                                <th>新架构</th>
                            </tr>
                            <tr>
                                <td><strong>VM数量</strong></td>
                                <td>4个 (3etcd + 1lb)</td>
                                <td>3个 (etcd集成lb)</td>
                            </tr>
                            <tr>
                                <td><strong>网口数量</strong></td>
                                <td>3个 (业务+集群+管理)</td>
                                <td>2个 (业务+管理)</td>
                            </tr>
                            <tr>
                                <td><strong>VLAN数量</strong></td>
                                <td>3个 (100,200,300)</td>
                                <td>2个 (100,300)</td>
                            </tr>
                            <tr>
                                <td><strong>集群通信</strong></td>
                                <td>专用网络 (VLAN 200)</td>
                                <td>业务网络 (VLAN 100)</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>

            <div class="feature-grid">
                <div class="feature-card">
                    <h3>🔄 VIP方案对比</h3>
                    <table class="comparison-table">
                        <tr>
                            <th>特性</th>
                            <th>主备模式 (VRRP)</th>
                            <th>集群模式 (LB)</th>
                        </tr>
                        <tr>
                            <td><strong>故障转移</strong></td>
                            <td>~8秒</td>
                            <td>&lt;1秒</td>
                        </tr>
                        <tr>
                            <td><strong>负载分担</strong></td>
                            <td>单点服务</td>
                            <td>多点均衡</td>
                        </tr>
                        <tr>
                            <td><strong>实现方式</strong></td>
                            <td>keepalived + VRRP</td>
                            <td>HAProxy/Nginx</td>
                        </tr>
                    </table>
                </div>

                <div class="feature-card">
                    <h3>⚖️ HAProxy负载均衡配置</h3>
                    <div class="code-block">
# HAProxy配置文件
frontend etcd_frontend
    bind ***************:2379
    default_backend etcd_backend

backend etcd_backend
    balance roundrobin
    option tcp-check
    server etcd1 **************:2379 check
    server etcd2 **************:2379 check
    server etcd3 **************:2379 check

# 客户端通过VIP访问
etcdctl --endpoints=https://***************:2379 get /test
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>⚙️ etcd集群配置</h2>
            <div class="feature-grid">
                <div class="feature-card">
                    <h3>📋 集群初始化配置</h3>
                    <div class="code-block">
# etcd-node-1 配置文件
ETCD_NAME=etcd-node-1
ETCD_LISTEN_CLIENT_URLS=https://**************:2379
ETCD_LISTEN_PEER_URLS=https://**************:2380
ETCD_ADVERTISE_CLIENT_URLS=https://**************:2379
ETCD_INITIAL_ADVERTISE_PEER_URLS=https://**************:2380
ETCD_INITIAL_CLUSTER=etcd-node-1=https://**************:2380,etcd-node-2=https://**************:2380,etcd-node-3=https://**************:2380
                    </div>
                </div>

                <div class="feature-card">
                    <h3>🔍 集群状态监控</h3>
                    <div class="code-block">
# 通过VIP检查集群健康 (推荐)
etcdctl --endpoints=https://***************:2379 endpoint health

# 查看集群成员
etcdctl --endpoints=https://***************:2379 member list

# 检查集群状态
etcdctl --endpoints=https://***************:2379 endpoint status

# 性能测试
etcdctl --endpoints=https://***************:2379 check perf
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📈 性能优化建议</h2>
            <div class="feature-grid">
                <div class="feature-card">
                    <h3>🚀 网络性能优化</h3>
                    <ul>
                        <li><strong>网卡多队列</strong>: ethtool -L eth0 combined 4</li>
                        <li><strong>CPU亲和性</strong>: 绑定网络中断到特定CPU</li>
                        <li><strong>TCP缓冲区</strong>: 调整内核网络参数</li>
                        <li><strong>DPDK加速</strong>: 高性能数据平面处理</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>💾 存储性能优化</h3>
                    <ul>
                        <li><strong>SSD存储</strong>: 使用高性能SSD磁盘</li>
                        <li><strong>文件系统</strong>: 推荐使用ext4或xfs</li>
                        <li><strong>I/O调度</strong>: 设置为deadline或noop</li>
                        <li><strong>预分配</strong>: 启用etcd数据预分配</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🔧 故障排查指南</h2>
            <div class="network-diagram">
                <h3>常见问题诊断步骤:</h3>
                <div class="code-block">
# 1. 检查OVS配置
ovs-vsctl show
ovs-ofctl dump-flows br0

# 2. 检查网络连通性
ping **************  # 集群内部通信
ping **************  # 客户端访问

# 3. 检查etcd服务状态
systemctl status etcd
journalctl -u etcd -f

# 4. 网络抓包分析
tcpdump -i br0 -n host **************
tcpdump -i eth0 -n vlan 200

# 5. 性能测试
iperf3 -s  # 在一个节点
iperf3 -c **************  # 在另一个节点
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📋 部署建议</h2>
            <div class="feature-grid">
                <div class="feature-card">
                    <h3>✅ 选择Trunk模式的场景</h3>
                    <ul>
                        <li>VM网络接口数量有限</li>
                        <li>需要支持多个VLAN</li>
                        <li>网络配置需要灵活扩展</li>
                        <li>对网络性能要求不是特别高</li>
                        <li>熟悉VLAN标签技术</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>✅ 选择Access模式的场景</h3>
                    <ul>
                        <li>对网络性能要求较高</li>
                        <li>需要严格的网络隔离</li>
                        <li>简化VM网络配置</li>
                        <li>便于故障排查和监控</li>
                        <li>安全要求较高的环境</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🎯 总结</h2>
            <p>单宿主机etcd集群架构适合开发测试环境和小规模部署场景。通过合理的OVS VLAN配置，可以在单台物理机上实现完整的etcd集群功能，同时保证网络隔离和性能要求。</p>
            <p><strong>推荐配置</strong>：对于生产环境，建议使用Access模式以获得更好的性能和安全性；对于开发测试环境，Trunk模式提供了更好的灵活性和资源利用率。</p>
        </div>
    </div>
</body>
</html>
