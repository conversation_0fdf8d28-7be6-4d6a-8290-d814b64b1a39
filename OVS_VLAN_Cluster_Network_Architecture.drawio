<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2025-07-29T00:00:00.000Z" agent="Augment Agent" etag="cluster-version" version="24.7.7">
  <diagram name="OVS VLAN 集群网络架构与实现方案" id="ovs-vlan-cluster-architecture">
    <mxGraphModel dx="1600" dy="1000" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1400" pageHeight="2000" math="0" shadow="0">
      <root>
        <mxCell id="0"/>
        <mxCell id="1" parent="0"/>

        <!-- ========== 集群架构总览 ========== -->
        <mxCell id="cluster-overview-title" value="OVS VLAN 虚拟机集群网络架构总览" style="text;html=1;strokeColor=#6c8ebf;fillColor=#dae8fc;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=20;fontStyle=1;strokeWidth=3;" vertex="1" parent="1">
          <mxGeometry x="50" y="20" width="1300" height="50" as="geometry"/>
        </mxCell>

        <!-- 集群架构说明 -->
        <mxCell id="cluster-description" value="&lt;b&gt;集群架构特点:&lt;/b&gt;&lt;br&gt;• 5节点分布式集群，支持水平扩展&lt;br&gt;• 负载均衡和服务发现机制&lt;br&gt;• 多VLAN网络隔离和流量分离&lt;br&gt;• 容器化应用支持 (Kubernetes)&lt;br&gt;• 高可用性和故障自愈能力" style="shape=callout;whiteSpace=wrap;html=1;perimeter=calloutPerimeter;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=12;align=left;" vertex="1" parent="1">
          <mxGeometry x="50" y="90" width="400" height="120" as="geometry"/>
        </mxCell>

        <!-- ========== Trunk模式集群网络拓扑架构 ========== -->
        <mxCell id="trunk-cluster-title" value="Trunk模式集群网络拓扑架构" style="text;html=1;strokeColor=#6c8ebf;fillColor=#dae8fc;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=18;fontStyle=1;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="50" y="240" width="1300" height="40" as="geometry"/>
        </mxCell>

        <!-- 核心交换机集群 -->
        <mxCell id="core-switch-cluster" value="核心交换机集群&lt;br&gt;&lt;b&gt;Trunk 端口配置&lt;/b&gt;&lt;br&gt;允许 VLAN: 100,200,300,400&lt;br&gt;Native VLAN: 1&lt;br&gt;LACP 链路聚合" style="shape=rectangle;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="600" y="300" width="200" height="100" as="geometry"/>
        </mxCell>

        <!-- 交换机配置示例 -->
        <mxCell id="switch-trunk-config" value="&lt;b&gt;核心交换机配置:&lt;/b&gt;&lt;br&gt;interface range GigabitEthernet0/1-5&lt;br&gt; switchport mode trunk&lt;br&gt; switchport trunk allowed vlan 100,200,300,400&lt;br&gt; switchport trunk native vlan 1&lt;br&gt; channel-group 1 mode active&lt;br&gt;&lt;br&gt;interface port-channel1&lt;br&gt; switchport mode trunk&lt;br&gt; spanning-tree portfast trunk" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=10;align=left;" vertex="1" parent="1">
          <mxGeometry x="850" y="300" width="300" height="140" as="geometry"/>
        </mxCell>

        <!-- 宿主机集群节点 -->
        <mxCell id="host-cluster-1" value="宿主机 Node-1&lt;br&gt;&lt;b&gt;OVS 网桥: br0&lt;/b&gt;&lt;br&gt;模式: Trunk&lt;br&gt;物理网卡: eth0&lt;br&gt;角色: Master Node" style="shape=rectangle;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="50" y="450" width="160" height="100" as="geometry"/>
        </mxCell>

        <mxCell id="host-cluster-2" value="宿主机 Node-2&lt;br&gt;&lt;b&gt;OVS 网桥: br0&lt;/b&gt;&lt;br&gt;模式: Trunk&lt;br&gt;物理网卡: eth0&lt;br&gt;角色: Worker Node" style="shape=rectangle;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="250" y="450" width="160" height="100" as="geometry"/>
        </mxCell>

        <mxCell id="host-cluster-3" value="宿主机 Node-3&lt;br&gt;&lt;b&gt;OVS 网桥: br0&lt;/b&gt;&lt;br&gt;模式: Trunk&lt;br&gt;物理网卡: eth0&lt;br&gt;角色: Worker Node" style="shape=rectangle;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="850" y="450" width="160" height="100" as="geometry"/>
        </mxCell>

        <mxCell id="host-cluster-4" value="宿主机 Node-4&lt;br&gt;&lt;b&gt;OVS 网桥: br0&lt;/b&gt;&lt;br&gt;模式: Trunk&lt;br&gt;物理网卡: eth0&lt;br&gt;角色: Worker Node" style="shape=rectangle;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="1050" y="450" width="160" height="100" as="geometry"/>
        </mxCell>

        <mxCell id="host-cluster-5" value="宿主机 Node-5&lt;br&gt;&lt;b&gt;OVS 网桥: br0&lt;/b&gt;&lt;br&gt;模式: Trunk&lt;br&gt;物理网卡: eth0&lt;br&gt;角色: Worker Node" style="shape=rectangle;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="1250" y="450" width="160" height="100" as="geometry"/>
        </mxCell>

        <!-- 集群OVS配置 -->
        <mxCell id="cluster-ovs-config" value="&lt;b&gt;集群OVS统一配置:&lt;/b&gt;&lt;br&gt;# 创建OVS网桥&lt;br&gt;ovs-vsctl add-br br0&lt;br&gt;ovs-vsctl add-port br0 eth0&lt;br&gt;ovs-vsctl set port eth0 trunk=100,200,300,400&lt;br&gt;&lt;br&gt;# 配置VLAN接口&lt;br&gt;ovs-vsctl add-port br0 vlan100 tag=100 -- set interface vlan100 type=internal&lt;br&gt;ovs-vsctl add-port br0 vlan200 tag=200 -- set interface vlan200 type=internal&lt;br&gt;&lt;br&gt;# 启用流表缓存&lt;br&gt;ovs-vsctl set bridge br0 datapath_type=netdev&lt;br&gt;ovs-vsctl set bridge br0 other-config:flow-eviction-threshold=1000" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f8cecc;strokeColor=#b85450;fontSize=9;align=left;" vertex="1" parent="1">
          <mxGeometry x="450" y="450" width="350" height="180" as="geometry"/>
        </mxCell>

        <!-- VM集群节点 -->
        <mxCell id="vm-cluster-master" value="VM-Master (K8s Master)&lt;br&gt;&lt;b&gt;多VLAN接口配置&lt;/b&gt;&lt;br&gt;eth0.100: 192.168.100.10/24&lt;br&gt;eth0.200: 192.168.200.10/24&lt;br&gt;eth0.300: 192.168.300.10/24&lt;br&gt;服务: etcd, kube-apiserver" style="shape=rectangle;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="50" y="580" width="160" height="120" as="geometry"/>
        </mxCell>

        <mxCell id="vm-cluster-worker1" value="VM-Worker-1&lt;br&gt;&lt;b&gt;应用服务节点&lt;/b&gt;&lt;br&gt;eth0.100: **************/24&lt;br&gt;eth0.200: 192.168.200.11/24&lt;br&gt;服务: kubelet, docker&lt;br&gt;Pod网络: 10.244.1.0/24" style="shape=rectangle;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="250" y="580" width="160" height="120" as="geometry"/>
        </mxCell>

        <mxCell id="vm-cluster-worker2" value="VM-Worker-2&lt;br&gt;&lt;b&gt;应用服务节点&lt;/b&gt;&lt;br&gt;eth0.100: 192.168.100.12/24&lt;br&gt;eth0.200: 192.168.200.12/24&lt;br&gt;服务: kubelet, docker&lt;br&gt;Pod网络: 10.244.2.0/24" style="shape=rectangle;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="850" y="580" width="160" height="120" as="geometry"/>
        </mxCell>

        <mxCell id="vm-cluster-worker3" value="VM-Worker-3&lt;br&gt;&lt;b&gt;应用服务节点&lt;/b&gt;&lt;br&gt;eth0.100: 192.168.100.13/24&lt;br&gt;eth0.200: 192.168.200.13/24&lt;br&gt;服务: kubelet, docker&lt;br&gt;Pod网络: 10.244.3.0/24" style="shape=rectangle;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="1050" y="580" width="160" height="120" as="geometry"/>
        </mxCell>

        <mxCell id="vm-cluster-worker4" value="VM-Worker-4&lt;br&gt;&lt;b&gt;应用服务节点&lt;/b&gt;&lt;br&gt;eth0.100: **************/24&lt;br&gt;eth0.200: **************/24&lt;br&gt;服务: kubelet, docker&lt;br&gt;Pod网络: **********/24" style="shape=rectangle;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="1250" y="580" width="160" height="120" as="geometry"/>
        </mxCell>

        <!-- 负载均衡器 -->
        <mxCell id="load-balancer" value="负载均衡器 (HAProxy/Nginx)&lt;br&gt;&lt;b&gt;VIP: ***************&lt;/b&gt;&lt;br&gt;后端节点: **************-14&lt;br&gt;健康检查: HTTP/TCP&lt;br&gt;算法: round-robin" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="600" y="720" width="200" height="100" as="geometry"/>
        </mxCell>

        <!-- 连接线 - Trunk模式 -->
        <mxCell id="trunk-conn-switch-1" style="endArrow=classic;edgeStyle=orthogonalEdgeStyle;strokeWidth=3;strokeColor=#6c8ebf;" edge="1" parent="1" source="core-switch-cluster" target="host-cluster-1">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        <mxCell id="trunk-conn-switch-2" style="endArrow=classic;edgeStyle=orthogonalEdgeStyle;strokeWidth=3;strokeColor=#6c8ebf;" edge="1" parent="1" source="core-switch-cluster" target="host-cluster-2">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        <mxCell id="trunk-conn-switch-3" style="endArrow=classic;edgeStyle=orthogonalEdgeStyle;strokeWidth=3;strokeColor=#6c8ebf;" edge="1" parent="1" source="core-switch-cluster" target="host-cluster-3">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        <mxCell id="trunk-conn-switch-4" style="endArrow=classic;edgeStyle=orthogonalEdgeStyle;strokeWidth=3;strokeColor=#6c8ebf;" edge="1" parent="1" source="core-switch-cluster" target="host-cluster-4">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        <mxCell id="trunk-conn-switch-5" style="endArrow=classic;edgeStyle=orthogonalEdgeStyle;strokeWidth=3;strokeColor=#6c8ebf;" edge="1" parent="1" source="core-switch-cluster" target="host-cluster-5">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <!-- VM连接线 -->
        <mxCell id="vm-conn-1" style="endArrow=classic;edgeStyle=orthogonalEdgeStyle;strokeWidth=2;strokeColor=#9673a6;" edge="1" parent="1" source="host-cluster-1" target="vm-cluster-master">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        <mxCell id="vm-conn-2" style="endArrow=classic;edgeStyle=orthogonalEdgeStyle;strokeWidth=2;strokeColor=#9673a6;" edge="1" parent="1" source="host-cluster-2" target="vm-cluster-worker1">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        <mxCell id="vm-conn-3" style="endArrow=classic;edgeStyle=orthogonalEdgeStyle;strokeWidth=2;strokeColor=#9673a6;" edge="1" parent="1" source="host-cluster-3" target="vm-cluster-worker2">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        <mxCell id="vm-conn-4" style="endArrow=classic;edgeStyle=orthogonalEdgeStyle;strokeWidth=2;strokeColor=#9673a6;" edge="1" parent="1" source="host-cluster-4" target="vm-cluster-worker3">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        <mxCell id="vm-conn-5" style="endArrow=classic;edgeStyle=orthogonalEdgeStyle;strokeWidth=2;strokeColor=#9673a6;" edge="1" parent="1" source="host-cluster-5" target="vm-cluster-worker4">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <!-- ========== Access模式集群网络拓扑架构 ========== -->
        <mxCell id="access-cluster-title" value="Access模式集群网络拓扑架构" style="text;html=1;strokeColor=#6c8ebf;fillColor=#dae8fc;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=18;fontStyle=1;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="50" y="860" width="1300" height="40" as="geometry"/>
        </mxCell>

        <!-- Access模式交换机 -->
        <mxCell id="access-switch-cluster" value="接入交换机集群&lt;br&gt;&lt;b&gt;Access 端口配置&lt;/b&gt;&lt;br&gt;VLAN 100: 业务网络&lt;br&gt;VLAN 200: 管理网络&lt;br&gt;VLAN 300: 存储网络" style="shape=rectangle;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="600" y="920" width="200" height="100" as="geometry"/>
        </mxCell>

        <!-- Access模式配置示例 -->
        <mxCell id="access-switch-config" value="&lt;b&gt;接入交换机配置:&lt;/b&gt;&lt;br&gt;# 业务网络端口&lt;br&gt;interface range GigabitEthernet0/1-10&lt;br&gt; switchport mode access&lt;br&gt; switchport access vlan 100&lt;br&gt;&lt;br&gt;# 管理网络端口&lt;br&gt;interface range GigabitEthernet0/11-15&lt;br&gt; switchport mode access&lt;br&gt; switchport access vlan 200&lt;br&gt;&lt;br&gt;# 存储网络端口&lt;br&gt;interface range GigabitEthernet0/16-20&lt;br&gt; switchport mode access&lt;br&gt; switchport access vlan 300" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=9;align=left;" vertex="1" parent="1">
          <mxGeometry x="850" y="920" width="300" height="160" as="geometry"/>
        </mxCell>

        <!-- Access模式宿主机 -->
        <mxCell id="access-host-1" value="宿主机 Access-1&lt;br&gt;&lt;b&gt;OVS 网桥: br-access&lt;/b&gt;&lt;br&gt;模式: Access&lt;br&gt;VLAN: 100 (业务)&lt;br&gt;简化配置" style="shape=rectangle;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="50" y="1070" width="160" height="100" as="geometry"/>
        </mxCell>

        <mxCell id="access-host-2" value="宿主机 Access-2&lt;br&gt;&lt;b&gt;OVS 网桥: br-access&lt;/b&gt;&lt;br&gt;模式: Access&lt;br&gt;VLAN: 200 (管理)&lt;br&gt;简化配置" style="shape=rectangle;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="250" y="1070" width="160" height="100" as="geometry"/>
        </mxCell>

        <mxCell id="access-host-3" value="宿主机 Access-3&lt;br&gt;&lt;b&gt;OVS 网桥: br-access&lt;/b&gt;&lt;br&gt;模式: Access&lt;br&gt;VLAN: 300 (存储)&lt;br&gt;简化配置" style="shape=rectangle;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="1050" y="1070" width="160" height="100" as="geometry"/>
        </mxCell>

        <!-- Access模式OVS配置 -->
        <mxCell id="access-ovs-config" value="&lt;b&gt;Access模式OVS配置:&lt;/b&gt;&lt;br&gt;# 业务网络配置&lt;br&gt;ovs-vsctl add-br br-access&lt;br&gt;ovs-vsctl add-port br-access eth0&lt;br&gt;ovs-vsctl set port br-access tag=100&lt;br&gt;&lt;br&gt;# VM端口自动继承VLAN&lt;br&gt;ovs-vsctl add-port br-access vnet0 tag=100&lt;br&gt;&lt;br&gt;# 管理网络配置&lt;br&gt;ovs-vsctl add-br br-mgmt&lt;br&gt;ovs-vsctl set port br-mgmt tag=200&lt;br&gt;&lt;br&gt;# 存储网络配置&lt;br&gt;ovs-vsctl add-br br-storage&lt;br&gt;ovs-vsctl set port br-storage tag=300" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f8cecc;strokeColor=#b85450;fontSize=9;align=left;" vertex="1" parent="1">
          <mxGeometry x="450" y="1070" width="350" height="180" as="geometry"/>
        </mxCell>

        <!-- Access模式VM -->
        <mxCell id="access-vm-1" value="VM-Business&lt;br&gt;&lt;b&gt;业务应用节点&lt;/b&gt;&lt;br&gt;eth0: 192.168.100.20/24&lt;br&gt;无VLAN标签&lt;br&gt;应用: Web服务" style="shape=rectangle;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="50" y="1200" width="160" height="100" as="geometry"/>
        </mxCell>

        <mxCell id="access-vm-2" value="VM-Management&lt;br&gt;&lt;b&gt;管理服务节点&lt;/b&gt;&lt;br&gt;eth0: 192.168.200.20/24&lt;br&gt;无VLAN标签&lt;br&gt;应用: 监控系统" style="shape=rectangle;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="250" y="1200" width="160" height="100" as="geometry"/>
        </mxCell>

        <mxCell id="access-vm-3" value="VM-Storage&lt;br&gt;&lt;b&gt;存储服务节点&lt;/b&gt;&lt;br&gt;eth0: 192.168.300.20/24&lt;br&gt;无VLAN标签&lt;br&gt;应用: Ceph/GlusterFS" style="shape=rectangle;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="1050" y="1200" width="160" height="100" as="geometry"/>
        </mxCell>

        <!-- Access模式连接线 -->
        <mxCell id="access-conn-1" style="endArrow=classic;edgeStyle=orthogonalEdgeStyle;strokeWidth=3;strokeColor=#6c8ebf;" edge="1" parent="1" source="access-switch-cluster" target="access-host-1">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        <mxCell id="access-conn-2" style="endArrow=classic;edgeStyle=orthogonalEdgeStyle;strokeWidth=3;strokeColor=#6c8ebf;" edge="1" parent="1" source="access-switch-cluster" target="access-host-2">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        <mxCell id="access-conn-3" style="endArrow=classic;edgeStyle=orthogonalEdgeStyle;strokeWidth=3;strokeColor=#6c8ebf;" edge="1" parent="1" source="access-switch-cluster" target="access-host-3">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <mxCell id="access-vm-conn-1" style="endArrow=classic;edgeStyle=orthogonalEdgeStyle;strokeWidth=2;strokeColor=#9673a6;" edge="1" parent="1" source="access-host-1" target="access-vm-1">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        <mxCell id="access-vm-conn-2" style="endArrow=classic;edgeStyle=orthogonalEdgeStyle;strokeWidth=2;strokeColor=#9673a6;" edge="1" parent="1" source="access-host-2" target="access-vm-2">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        <mxCell id="access-vm-conn-3" style="endArrow=classic;edgeStyle=orthogonalEdgeStyle;strokeWidth=2;strokeColor=#9673a6;" edge="1" parent="1" source="access-host-3" target="access-vm-3">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <!-- ========== 集群管理和服务发现 ========== -->
        <mxCell id="cluster-management-title" value="集群管理和服务发现架构" style="text;html=1;strokeColor=#6c8ebf;fillColor=#dae8fc;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=18;fontStyle=1;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="50" y="1340" width="1300" height="40" as="geometry"/>
        </mxCell>

        <!-- Kubernetes集群组件 -->
        <mxCell id="k8s-master" value="Kubernetes Master&lt;br&gt;&lt;b&gt;集群控制平面&lt;/b&gt;&lt;br&gt;• kube-apiserver&lt;br&gt;• etcd 集群&lt;br&gt;• kube-scheduler&lt;br&gt;• kube-controller-manager" style="shape=rectangle;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="50" y="1400" width="200" height="120" as="geometry"/>
        </mxCell>

        <mxCell id="service-discovery" value="服务发现 (etcd/Consul)&lt;br&gt;&lt;b&gt;服务注册中心&lt;/b&gt;&lt;br&gt;• 服务注册和发现&lt;br&gt;• 健康检查&lt;br&gt;• 配置管理&lt;br&gt;• 分布式锁" style="shape=hexagon;perimeter=hexagonPerimeter2;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="300" y="1400" width="200" height="120" as="geometry"/>
        </mxCell>

        <mxCell id="ingress-controller" value="Ingress Controller&lt;br&gt;&lt;b&gt;流量入口控制&lt;/b&gt;&lt;br&gt;• Nginx/Traefik&lt;br&gt;• SSL终止&lt;br&gt;• 路由规则&lt;br&gt;• 负载均衡" style="shape=trapezoid;perimeter=trapezoidPerimeter;whiteSpace=wrap;html=1;fixedSize=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="550" y="1400" width="200" height="120" as="geometry"/>
        </mxCell>

        <mxCell id="monitoring-stack" value="监控告警系统&lt;br&gt;&lt;b&gt;Prometheus + Grafana&lt;/b&gt;&lt;br&gt;• 指标收集&lt;br&gt;• 告警规则&lt;br&gt;• 可视化面板&lt;br&gt;• 日志聚合" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="800" y="1400" width="200" height="120" as="geometry"/>
        </mxCell>

        <mxCell id="storage-cluster" value="分布式存储集群&lt;br&gt;&lt;b&gt;Ceph/GlusterFS&lt;/b&gt;&lt;br&gt;• 块存储 (RBD)&lt;br&gt;• 文件存储 (CephFS)&lt;br&gt;• 对象存储 (S3)&lt;br&gt;• 数据副本" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="1050" y="1400" width="200" height="120" as="geometry"/>
        </mxCell>

        <!-- 集群网络配置详情 -->
        <mxCell id="cluster-network-config" value="&lt;b&gt;Kubernetes网络配置:&lt;/b&gt;&lt;br&gt;# CNI插件配置 (Flannel/Calico)&lt;br&gt;kubectl apply -f https://raw.githubusercontent.com/coreos/flannel/master/Documentation/kube-flannel.yml&lt;br&gt;&lt;br&gt;# 服务网格配置&lt;br&gt;apiVersion: v1&lt;br&gt;kind: Service&lt;br&gt;metadata:&lt;br&gt;  name: web-service&lt;br&gt;spec:&lt;br&gt;  selector:&lt;br&gt;    app: web&lt;br&gt;  ports:&lt;br&gt;  - port: 80&lt;br&gt;    targetPort: 8080&lt;br&gt;  type: LoadBalancer&lt;br&gt;&lt;br&gt;# Ingress配置&lt;br&gt;apiVersion: networking.k8s.io/v1&lt;br&gt;kind: Ingress&lt;br&gt;metadata:&lt;br&gt;  name: web-ingress&lt;br&gt;spec:&lt;br&gt;  rules:&lt;br&gt;  - host: app.example.com&lt;br&gt;    http:&lt;br&gt;      paths:&lt;br&gt;      - path: /&lt;br&gt;        pathType: Prefix&lt;br&gt;        backend:&lt;br&gt;          service:&lt;br&gt;            name: web-service&lt;br&gt;            port:&lt;br&gt;              number: 80" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=8;align=left;" vertex="1" parent="1">
          <mxGeometry x="1300" y="1400" width="350" height="300" as="geometry"/>
        </mxCell>

        <!-- ========== 技术实现参考与配置指南 ========== -->
        <mxCell id="tech-reference-title" value="技术实现参考与配置指南" style="text;html=1;strokeColor=#6c8ebf;fillColor=#dae8fc;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=18;fontStyle=1;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="50" y="1560" width="1300" height="40" as="geometry"/>
        </mxCell>

        <!-- 集群部署脚本 -->
        <mxCell id="cluster-deployment" value="&lt;b&gt;集群自动化部署脚本:&lt;/b&gt;&lt;br&gt;&lt;br&gt;#!/bin/bash&lt;br&gt;# 集群初始化脚本&lt;br&gt;&lt;br&gt;# 1. 安装Docker和Kubernetes&lt;br&gt;curl -fsSL https://get.docker.com | sh&lt;br&gt;curl -s https://packages.cloud.google.com/apt/doc/apt-key.gpg | apt-key add -&lt;br&gt;echo &quot;deb https://apt.kubernetes.io/ kubernetes-xenial main&quot; &gt; /etc/apt/sources.list.d/kubernetes.list&lt;br&gt;apt-get update &amp;&amp; apt-get install -y kubelet kubeadm kubectl&lt;br&gt;&lt;br&gt;# 2. 配置OVS网桥&lt;br&gt;ovs-vsctl add-br br0&lt;br&gt;ovs-vsctl add-port br0 eth0&lt;br&gt;ovs-vsctl set port eth0 trunk=100,200,300,400&lt;br&gt;&lt;br&gt;# 3. 初始化Kubernetes集群&lt;br&gt;kubeadm init --pod-network-cidr=10.244.0.0/16&lt;br&gt;&lt;br&gt;# 4. 安装CNI网络插件&lt;br&gt;kubectl apply -f https://raw.githubusercontent.com/coreos/flannel/master/Documentation/kube-flannel.yml&lt;br&gt;&lt;br&gt;# 5. 配置负载均衡器&lt;br&gt;kubectl apply -f metallb-config.yaml" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=9;align=left;" vertex="1" parent="1">
          <mxGeometry x="50" y="1620" width="400" height="300" as="geometry"/>
        </mxCell>

        <!-- 网络性能优化 -->
        <mxCell id="network-optimization" value="&lt;b&gt;网络性能优化配置:&lt;/b&gt;&lt;br&gt;&lt;br&gt;&lt;b&gt;1. OVS性能调优:&lt;/b&gt;&lt;br&gt;# 启用DPDK加速&lt;br&gt;ovs-vsctl set Open_vSwitch . other_config:dpdk-init=true&lt;br&gt;ovs-vsctl set bridge br0 datapath_type=netdev&lt;br&gt;&lt;br&gt;# 配置CPU亲和性&lt;br&gt;ovs-vsctl set Open_vSwitch . other_config:pmd-cpu-mask=0x6&lt;br&gt;&lt;br&gt;&lt;b&gt;2. 网卡多队列配置:&lt;/b&gt;&lt;br&gt;ethtool -L eth0 combined 4&lt;br&gt;echo 2 &gt; /sys/class/net/eth0/queues/rx-0/rps_cpus&lt;br&gt;&lt;br&gt;&lt;b&gt;3. 内核参数优化:&lt;/b&gt;&lt;br&gt;echo 'net.core.rmem_max = 134217728' &gt;&gt; /etc/sysctl.conf&lt;br&gt;echo 'net.core.wmem_max = 134217728' &gt;&gt; /etc/sysctl.conf&lt;br&gt;echo 'net.ipv4.tcp_rmem = 4096 87380 134217728' &gt;&gt; /etc/sysctl.conf&lt;br&gt;&lt;br&gt;&lt;b&gt;4. 容器网络优化:&lt;/b&gt;&lt;br&gt;# 配置Pod网络带宽限制&lt;br&gt;kubectl annotate pod my-pod kubernetes.io/ingress-bandwidth=100M&lt;br&gt;kubectl annotate pod my-pod kubernetes.io/egress-bandwidth=100M" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=9;align=left;" vertex="1" parent="1">
          <mxGeometry x="480" y="1620" width="400" height="300" as="geometry"/>
        </mxCell>

        <!-- 监控和故障排查 -->
        <mxCell id="monitoring-troubleshooting" value="&lt;b&gt;集群监控和故障排查:&lt;/b&gt;&lt;br&gt;&lt;br&gt;&lt;b&gt;1. 网络连通性检查:&lt;/b&gt;&lt;br&gt;# 检查OVS状态&lt;br&gt;ovs-vsctl show&lt;br&gt;ovs-ofctl dump-flows br0&lt;br&gt;&lt;br&gt;# 检查VLAN配置&lt;br&gt;ovs-vsctl list port&lt;br&gt;ovs-vsctl get port eth0 tag&lt;br&gt;&lt;br&gt;&lt;b&gt;2. Kubernetes集群状态:&lt;/b&gt;&lt;br&gt;kubectl get nodes -o wide&lt;br&gt;kubectl get pods --all-namespaces&lt;br&gt;kubectl describe node &lt;node-name&gt;&lt;br&gt;&lt;br&gt;&lt;b&gt;3. 网络流量分析:&lt;/b&gt;&lt;br&gt;tcpdump -i br0 -n vlan&lt;br&gt;iperf3 -s  # 性能测试服务端&lt;br&gt;iperf3 -c &lt;server-ip&gt;  # 性能测试客户端&lt;br&gt;&lt;br&gt;&lt;b&gt;4. 服务发现测试:&lt;/b&gt;&lt;br&gt;nslookup kubernetes.default.svc.cluster.local&lt;br&gt;curl -k https://kubernetes.default.svc.cluster.local&lt;br&gt;&lt;br&gt;&lt;b&gt;5. 负载均衡验证:&lt;/b&gt;&lt;br&gt;kubectl get svc&lt;br&gt;kubectl get endpoints" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f8cecc;strokeColor=#b85450;fontSize=9;align=left;" vertex="1" parent="1">
          <mxGeometry x="910" y="1620" width="400" height="300" as="geometry"/>
        </mxCell>

        <!-- 集群扩展和维护 -->
        <mxCell id="cluster-scaling" value="&lt;b&gt;集群扩展和维护最佳实践:&lt;/b&gt;&lt;br&gt;&lt;br&gt;&lt;b&gt;1. 水平扩展:&lt;/b&gt;&lt;br&gt;# 添加新的Worker节点&lt;br&gt;kubeadm token create --print-join-command&lt;br&gt;# 在新节点上执行join命令&lt;br&gt;&lt;br&gt;&lt;b&gt;2. 自动扩缩容:&lt;/b&gt;&lt;br&gt;kubectl apply -f cluster-autoscaler.yaml&lt;br&gt;kubectl autoscale deployment web-app --cpu-percent=50 --min=3 --max=10&lt;br&gt;&lt;br&gt;&lt;b&gt;3. 滚动更新:&lt;/b&gt;&lt;br&gt;kubectl set image deployment/web-app container=new-image:v2&lt;br&gt;kubectl rollout status deployment/web-app&lt;br&gt;&lt;br&gt;&lt;b&gt;4. 备份和恢复:&lt;/b&gt;&lt;br&gt;etcdctl snapshot save backup.db&lt;br&gt;etcdctl snapshot restore backup.db&lt;br&gt;&lt;br&gt;&lt;b&gt;5. 网络策略:&lt;/b&gt;&lt;br&gt;apiVersion: networking.k8s.io/v1&lt;br&gt;kind: NetworkPolicy&lt;br&gt;metadata:&lt;br&gt;  name: deny-all&lt;br&gt;spec:&lt;br&gt;  podSelector: {}&lt;br&gt;  policyTypes:&lt;br&gt;  - Ingress&lt;br&gt;  - Egress" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=9;align=left;" vertex="1" parent="1">
          <mxGeometry x="1340" y="1620" width="400" height="300" as="geometry"/>
        </mxCell>

        <!-- ========== 图例说明 ========== -->
        <mxCell id="legend-title" value="图例说明和架构对比" style="text;html=1;strokeColor=#6c8ebf;fillColor=#dae8fc;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=18;fontStyle=1;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="50" y="1960" width="1300" height="40" as="geometry"/>
        </mxCell>

        <!-- 颜色图例 -->
        <mxCell id="color-legend" value="&lt;table border=&quot;1&quot; style=&quot;border-collapse: collapse; width: 100%; font-size: 12px;&quot;&gt;&lt;tr&gt;&lt;th colspan=&quot;2&quot; style=&quot;background-color: #f0f0f0; padding: 8px;&quot;&gt;组件颜色编码&lt;/th&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td style=&quot;background-color: #dae8fc; padding: 8px;&quot;&gt;🔵 蓝色&lt;/td&gt;&lt;td style=&quot;padding: 8px;&quot;&gt;网络设备 (交换机、存储)&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td style=&quot;background-color: #e1d5e7; padding: 8px;&quot;&gt;🟣 紫色&lt;/td&gt;&lt;td style=&quot;padding: 8px;&quot;&gt;宿主机和OVS网桥&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td style=&quot;background-color: #d5e8d4; padding: 8px;&quot;&gt;🟢 绿色&lt;/td&gt;&lt;td style=&quot;padding: 8px;&quot;&gt;主节点和控制组件&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td style=&quot;background-color: #fff2cc; padding: 8px;&quot;&gt;🟡 黄色&lt;/td&gt;&lt;td style=&quot;padding: 8px;&quot;&gt;工作节点和服务发现&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td style=&quot;background-color: #f8cecc; padding: 8px;&quot;&gt;🔴 红色&lt;/td&gt;&lt;td style=&quot;padding: 8px;&quot;&gt;负载均衡和入口控制&lt;/td&gt;&lt;/tr&gt;&lt;/table&gt;" style="text;html=1;strokeColor=#6c8ebf;fillColor=none;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="50" y="2020" width="300" height="150" as="geometry"/>
        </mxCell>

        <!-- 架构对比 -->
        <mxCell id="architecture-comparison" value="&lt;table border=&quot;1&quot; style=&quot;border-collapse: collapse; width: 100%; font-size: 11px;&quot;&gt;&lt;tr&gt;&lt;th style=&quot;background-color: #f0f0f0; padding: 8px;&quot;&gt;特性&lt;/th&gt;&lt;th style=&quot;background-color: #f0f0f0; padding: 8px;&quot;&gt;主备模式&lt;/th&gt;&lt;th style=&quot;background-color: #f0f0f0; padding: 8px;&quot;&gt;集群模式&lt;/th&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td style=&quot;padding: 6px;&quot;&gt;&lt;b&gt;节点数量&lt;/b&gt;&lt;/td&gt;&lt;td style=&quot;padding: 6px;&quot;&gt;2个 (1主1备)&lt;/td&gt;&lt;td style=&quot;padding: 6px;&quot;&gt;3-N个 (可扩展)&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td style=&quot;padding: 6px;&quot;&gt;&lt;b&gt;可用性&lt;/b&gt;&lt;/td&gt;&lt;td style=&quot;padding: 6px;&quot;&gt;主备切换 (~8秒)&lt;/td&gt;&lt;td style=&quot;padding: 6px;&quot;&gt;分布式高可用 (&lt;1秒)&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td style=&quot;padding: 6px;&quot;&gt;&lt;b&gt;负载分担&lt;/b&gt;&lt;/td&gt;&lt;td style=&quot;padding: 6px;&quot;&gt;主节点承担所有负载&lt;/td&gt;&lt;td style=&quot;padding: 6px;&quot;&gt;多节点负载均衡&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td style=&quot;padding: 6px;&quot;&gt;&lt;b&gt;扩展性&lt;/b&gt;&lt;/td&gt;&lt;td style=&quot;padding: 6px;&quot;&gt;垂直扩展为主&lt;/td&gt;&lt;td style=&quot;padding: 6px;&quot;&gt;水平扩展优先&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td style=&quot;padding: 6px;&quot;&gt;&lt;b&gt;复杂度&lt;/b&gt;&lt;/td&gt;&lt;td style=&quot;padding: 6px;&quot;&gt;配置简单&lt;/td&gt;&lt;td style=&quot;padding: 6px;&quot;&gt;需要编排工具&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td style=&quot;padding: 6px;&quot;&gt;&lt;b&gt;适用场景&lt;/b&gt;&lt;/td&gt;&lt;td style=&quot;padding: 6px;&quot;&gt;传统应用、数据库&lt;/td&gt;&lt;td style=&quot;padding: 6px;&quot;&gt;微服务、容器化应用&lt;/td&gt;&lt;/tr&gt;&lt;/table&gt;" style="text;html=1;strokeColor=#6c8ebf;fillColor=none;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="380" y="2020" width="500" height="150" as="geometry"/>
        </mxCell>

        <!-- 技术栈对比 -->
        <mxCell id="tech-stack-comparison" value="&lt;b&gt;技术栈对比:&lt;/b&gt;&lt;br&gt;&lt;br&gt;&lt;b&gt;主备模式技术栈:&lt;/b&gt;&lt;br&gt;• VRRP (keepalived)&lt;br&gt;• 虚拟IP漂移&lt;br&gt;• 心跳检测&lt;br&gt;• 故障转移脚本&lt;br&gt;&lt;br&gt;&lt;b&gt;集群模式技术栈:&lt;/b&gt;&lt;br&gt;• Kubernetes/Docker Swarm&lt;br&gt;• 服务发现 (etcd/Consul)&lt;br&gt;• 负载均衡 (HAProxy/Nginx)&lt;br&gt;• 容器编排&lt;br&gt;• 服务网格 (Istio/Linkerd)&lt;br&gt;• 监控告警 (Prometheus)&lt;br&gt;&lt;br&gt;&lt;b&gt;网络特性对比:&lt;/b&gt;&lt;br&gt;• 主备: 单点网络，简单VLAN&lt;br&gt;• 集群: 多租户网络，复杂路由" style="shape=callout;whiteSpace=wrap;html=1;perimeter=calloutPerimeter;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=10;align=left;" vertex="1" parent="1">
          <mxGeometry x="920" y="2020" width="350" height="200" as="geometry"/>
        </mxCell>

      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
