/**
 * @file performance_comparison.c
 * @brief 性能对比测试：指针转换 vs memcpy
 * <AUTHOR> Agent
 * @date 2025-07-31
 */

#include "protocol_identifier.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>

/* 跨平台字节序转换 */
#ifdef _WIN32
    #include <winsock2.h>
    #pragma comment(lib, "ws2_32.lib")
#else
    #include <arpa/inet.h>
#endif

/**
 * @brief 使用memcpy的HSM协议识别（旧方法）
 */
int is_custom_hsm_format_memcpy(const unsigned char* data, size_t data_len) {
    if (!data || data_len < MIN_CUSTOM_PACKET_SIZE) {
        return 0;
    }
    
    /* 使用memcpy解析包头 */
    HSM_REQ_HEAD header;
    memcpy(&header, data, sizeof(HSM_REQ_HEAD));
    
    /* 转换字节序 */
    uint32_t tlen = ntohl(header.tlen);
    uint32_t checksum = ntohl(header.checksum);
    uint16_t action = ntohs(header.action);
    uint16_t version = ntohs(header.version);
    
    /* 基本验证 */
    if (tlen < MIN_CUSTOM_PACKET_SIZE || tlen > 1024*1024) {
        return 0;
    }
    
    if (action < 1 || action > 9999) {
        return 0;
    }
    
    uint8_t major_version = (version >> 8) & 0xFF;
    if (major_version > 10) {
        return 0;
    }
    
    return 1;
}

/**
 * @brief 使用指针转换的HSM协议识别（新方法）
 */
int is_custom_hsm_format_pointer(const unsigned char* data, size_t data_len) {
    if (!data || data_len < MIN_CUSTOM_PACKET_SIZE) {
        return 0;
    }
    
    /* 直接使用指针转换 */
    const HSM_REQ_HEAD *header = (const HSM_REQ_HEAD *)data;
    
    /* 转换字节序 */
    uint32_t tlen = ntohl(header->tlen);
    uint32_t checksum = ntohl(header->checksum);
    uint16_t action = ntohs(header->action);
    uint16_t version = ntohs(header->version);
    
    /* 基本验证 */
    if (tlen < MIN_CUSTOM_PACKET_SIZE || tlen > 1024*1024) {
        return 0;
    }
    
    if (action < 1 || action > 9999) {
        return 0;
    }
    
    uint8_t major_version = (version >> 8) & 0xFF;
    if (major_version > 10) {
        return 0;
    }
    
    return 1;
}

/**
 * @brief 创建测试用的HSM数据包
 */
void create_test_packet(unsigned char* buffer, size_t* len) {
    HSM_REQ_HEAD header;
    const char* payload = "Performance test payload data";
    size_t payload_len = strlen(payload);

    /* 计算正确的校验和 */
    uint32_t checksum = calculate_simple_checksum((unsigned char*)payload, payload_len);

    header.tlen = htonl(sizeof(HSM_REQ_HEAD) + payload_len);
    header.checksum = htonl(checksum);
    header.action = htons(1001);
    header.version = htons(0x0101);
    header.reserve = htonl(0);

    memcpy(buffer, &header, sizeof(HSM_REQ_HEAD));
    memcpy(buffer + sizeof(HSM_REQ_HEAD), payload, payload_len);
    *len = sizeof(HSM_REQ_HEAD) + payload_len;
}

/**
 * @brief 获取当前时间（毫秒）
 */
double get_time_ms() {
#ifdef _WIN32
    return (double)clock() / CLOCKS_PER_SEC * 1000.0;
#else
    struct timespec ts;
    clock_gettime(CLOCK_MONOTONIC, &ts);
    return ts.tv_sec * 1000.0 + ts.tv_nsec / 1000000.0;
#endif
}

/**
 * @brief 性能测试函数
 */
void performance_test() {
    const int iterations = 1000000;  // 100万次迭代
    unsigned char buffer[256];
    size_t len;
    
    create_test_packet(buffer, &len);
    
    printf("Performance Comparison Test\n");
    printf("===========================\n");
    printf("Test packet size: %zu bytes\n", len);
    printf("Iterations: %d\n\n", iterations);
    
    // 测试memcpy方法
    double start_time = get_time_ms();
    for (int i = 0; i < iterations; i++) {
        is_custom_hsm_format_memcpy(buffer, len);
    }
    double memcpy_time = get_time_ms() - start_time;
    
    // 测试指针转换方法
    start_time = get_time_ms();
    for (int i = 0; i < iterations; i++) {
        is_custom_hsm_format_pointer(buffer, len);
    }
    double pointer_time = get_time_ms() - start_time;
    
    // 测试当前实现
    start_time = get_time_ms();
    for (int i = 0; i < iterations; i++) {
        is_custom_hsm_format(buffer, len);
    }
    double current_time = get_time_ms() - start_time;
    
    // 输出结果
    printf("Results:\n");
    printf("--------\n");
    printf("memcpy method:     %.2f ms (%.3f μs per call)\n", 
           memcpy_time, memcpy_time * 1000.0 / iterations);
    printf("pointer method:    %.2f ms (%.3f μs per call)\n", 
           pointer_time, pointer_time * 1000.0 / iterations);
    printf("current method:    %.2f ms (%.3f μs per call)\n", 
           current_time, current_time * 1000.0 / iterations);
    
    printf("\nPerformance improvement:\n");
    printf("------------------------\n");
    if (memcpy_time > 0) {
        printf("Pointer vs memcpy: %.1f%% faster\n", 
               (memcpy_time - pointer_time) / memcpy_time * 100.0);
        printf("Current vs memcpy: %.1f%% faster\n", 
               (memcpy_time - current_time) / memcpy_time * 100.0);
    }
    
    // 验证结果一致性
    int result1 = is_custom_hsm_format_memcpy(buffer, len);
    int result2 = is_custom_hsm_format_pointer(buffer, len);
    int result3 = is_custom_hsm_format(buffer, len);
    
    printf("\nResult consistency check:\n");
    printf("-------------------------\n");
    printf("memcpy result:   %d\n", result1);
    printf("pointer result:  %d\n", result2);
    printf("current result:  %d\n", result3);
    printf("All methods consistent: %s\n", 
           (result1 == result2 && result2 == result3) ? "YES" : "NO");
}

/**
 * @brief 主函数
 */
int main() {
    printf("HSM Protocol Identification Performance Test\n");
    printf("============================================\n\n");
    
    performance_test();
    
    printf("\nConclusion:\n");
    printf("-----------\n");
    printf("Using direct pointer casting with 1-byte alignment (#pragma pack(1))\n");
    printf("provides better performance by eliminating the memcpy overhead.\n");
    printf("This is safe because:\n");
    printf("1. Structure is packed to 1-byte alignment\n");
    printf("2. We use const pointer to avoid modifying original data\n");
    printf("3. Byte order conversion is done on local variables\n");
    
    return 0;
}
