<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OVS VLAN HA 拓扑图 - 详细技术配置</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 1200px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border-left: 4px solid #007acc; background: #f8f9fa; }
        .highlight { background: #e7f3ff; padding: 10px; border-radius: 5px; margin: 10px 0; }
        .code { background: #f4f4f4; padding: 10px; border-radius: 3px; font-family: 'Courier New', monospace; }
        .feature-list { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 15px; }
        .feature-item { background: white; padding: 15px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .btn { display: inline-block; padding: 12px 24px; background: #007acc; color: white; text-decoration: none; border-radius: 5px; margin: 10px 5px; }
        .btn:hover { background: #005a9e; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 OVS VLAN 高可用网络架构与实现方案</h1>

        <div class="highlight">
            <p><strong>📋 最新版本特性：</strong> 包含完整的网络拓扑架构、技术实现细节、故障转移流程图和配置指南</p>
        </div>

        <div class="section">
            <h2>🚀 快速访问</h2>
            <a href="https://app.diagrams.net/?lightbox=1&highlight=0000ff&edit=_blank&layers=1&nav=1&title=OVS_VLAN_HA_Topology_Fixed.drawio#R" target="_blank" class="btn">
                📊 在 Draw.io 中打开拓扑图
            </a>
            <a href="OVS_VLAN_HA_Topology_Fixed.drawio" download class="btn">
                💾 下载 .drawio 文件
            </a>
        </div>

        <div class="section">
            <h2>🔧 详细优化内容</h2>
            <div class="feature-list">
                <div class="feature-item">
                    <h3>🏗️ Trunk模式网络拓扑架构</h3>
                    <ul>
                        <li>物理交换机 Trunk 端口配置示例</li>
                        <li>OVS 网桥 Trunk 模式设置命令</li>
                        <li>VLAN 标签处理流程说明</li>
                        <li>VM 内部 VLAN 接口配置（eth0.100）</li>
                    </ul>
                </div>

                <div class="feature-item">
                    <h3>🔌 Access模式网络拓扑架构</h3>
                    <ul>
                        <li>物理交换机 Access 端口配置</li>
                        <li>OVS 网桥 Access 模式设置</li>
                        <li>VLAN 标签自动添加/移除机制</li>
                        <li>简化的 VM 网络配置</li>
                    </ul>
                </div>

                <div class="feature-item">
                    <h3>⚡ VRRP高可用故障转移流程图</h3>
                    <ul>
                        <li>完整的 keepalived 配置文件</li>
                        <li>服务健康检查脚本</li>
                        <li>故障转移时间线（8秒完成切换）</li>
                        <li>VIP 漂移和免费 ARP 机制</li>
                    </ul>
                </div>

                <div class="feature-item">
                    <h3>🛠️ 技术实现参考与配置指南</h3>
                    <ul>
                        <li>OVS 常用命令参考手册</li>
                        <li>网络配置最佳实践</li>
                        <li>故障排查指南和诊断命令</li>
                        <li>性能优化建议</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📊 拓扑图结构</h2>
            <ol>
                <li><strong>Trunk模式网络拓扑架构</strong>
                    <ul>
                        <li>物理交换机 Trunk 配置和命令示例</li>
                        <li>宿主机 OVS 网桥 Trunk 设置</li>
                        <li>VM VLAN 接口配置（eth0.100）</li>
                        <li>VLAN 标签处理流程说明</li>
                    </ul>
                </li>
                <li><strong>Access模式网络拓扑架构</strong>
                    <ul>
                        <li>物理交换机 Access 配置</li>
                        <li>宿主机 OVS 网桥 Access 设置</li>
                        <li>VM 简化网络配置</li>
                        <li>VLAN 标签自动处理机制</li>
                    </ul>
                </li>
                <li><strong>VRRP高可用故障转移流程图</strong>
                    <ul>
                        <li>正常运行状态监控</li>
                        <li>故障检测和转移过程</li>
                        <li>完整的 keepalived 配置</li>
                        <li>故障转移时间线分析</li>
                    </ul>
                </li>
                <li><strong>技术实现参考与配置指南</strong>
                    <ul>
                        <li>OVS 命令参考手册</li>
                        <li>网络配置最佳实践</li>
                        <li>故障排查指南</li>
                        <li>性能优化建议</li>
                    </ul>
                </li>
            </ol>
        </div>

        <div class="section">
            <h2>🎨 视觉设计特性</h2>
            <div class="feature-list">
                <div class="feature-item">
                    <h3>🌈 颜色编码系统</h3>
                    <ul>
                        <li><span style="background:#dae8fc;padding:2px 8px;border-radius:3px;">蓝色</span> - 物理交换机</li>
                        <li><span style="background:#e1d5e7;padding:2px 8px;border-radius:3px;">紫色</span> - 宿主机/OVS网桥</li>
                        <li><span style="background:#d5e8d4;padding:2px 8px;border-radius:3px;">绿色</span> - 主节点/正常状态</li>
                        <li><span style="background:#fff2cc;padding:2px 8px;border-radius:3px;">黄色</span> - 备节点</li>
                        <li><span style="background:#f8cecc;padding:2px 8px;border-radius:3px;">红色</span> - 故障状态</li>
                    </ul>
                </div>

                <div class="feature-item">
                    <h3>📝 配置信息展示</h3>
                    <ul>
                        <li>详细的命令行配置示例</li>
                        <li>完整的配置文件内容</li>
                        <li>技术参数和最佳实践</li>
                        <li>故障排查步骤指南</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>💡 使用建议</h2>
            <div class="highlight">
                <p><strong>适用场景：</strong></p>
                <ul>
                    <li>🏢 企业级虚拟化环境网络规划</li>
                    <li>🔧 OpenStack/KVM 网络架构设计</li>
                    <li>📚 网络工程师技术培训和学习</li>
                    <li>🛠️ 生产环境故障排查参考</li>
                </ul>
            </div>

            <div class="code">
                <strong>快速部署命令示例：</strong><br>
                # 创建 OVS 网桥<br>
                ovs-vsctl add-br br0<br>
                ovs-vsctl add-port br0 eth0<br>
                ovs-vsctl set port eth0 trunk=100,200,300<br>
                <br>
                # 配置 VM VLAN 接口<br>
                ip link add link eth0 name eth0.100 type vlan id 100<br>
                ip addr add **************/24 dev eth0.100<br>
                ip link set eth0.100 up
            </div>
        </div>

        <div class="section">
            <h2>📞 技术支持</h2>
            <p>如果您在使用过程中遇到问题，可以参考图表中的故障排查指南，或查看 OVS 官方文档获取更多技术细节。</p>
        </div>
    </div>
</body>
</html>
