/**
 * @file protocol_identifier.h
 * @brief TCP协议识别接口 - 区分ASN.1和自定义协议
 * <AUTHOR> Agent
 * @date 2025-07-31
 */

#ifndef PROTOCOL_IDENTIFIER_H
#define PROTOCOL_IDENTIFIER_H

#include <stdint.h>
#include <stddef.h>

#ifdef __cplusplus
extern "C" {
#endif

/* 协议类型定义 */
#define PROTOCOL_ASN1       0    /* ASN.1 DER编码协议 */
#define PROTOCOL_CUSTOM     1    /* 自定义HSM协议 */
#define PROTOCOL_UNKNOWN   -1    /* 无法识别的协议 */

/* 最小数据包长度 */
#define MIN_ASN1_PACKET_SIZE    2    /* ASN.1最小：Tag(1) + Length(1) */
#define MIN_CUSTOM_PACKET_SIZE  16   /* HSM包头固定16字节 */

/* ASN.1 DER编码常见标签 */
#define ASN1_SEQUENCE       0x30    /* SEQUENCE */
#define ASN1_SET            0x31    /* SET */
#define ASN1_INTEGER        0x02    /* INTEGER */
#define ASN1_OCTET_STRING   0x04    /* OCTET STRING */
#define ASN1_NULL           0x05    /* NULL */
#define ASN1_OID            0x06    /* OBJECT IDENTIFIER */
#define ASN1_UTF8_STRING    0x0C    /* UTF8String */
#define ASN1_PRINTABLE_STR  0x13    /* PrintableString */
#define ASN1_T61_STRING     0x14    /* T61String */
#define ASN1_IA5_STRING     0x16    /* IA5String */
#define ASN1_UTC_TIME       0x17    /* UTCTime */
#define ASN1_GENERALIZED_TIME 0x18  /* GeneralizedTime */

/* 自定义协议包头结构 - 1字节对齐 */
#pragma pack(push, 1)
typedef struct hsm_req_head {
    uint32_t tlen;      /* 消息总长度 */
    uint32_t checksum;  /* 消息体的校验和 */
    uint16_t action;    /* 动作 = 函数的代号 */
    uint16_t version;   /* 主次版本号 */
    uint32_t reserve;   /* 预留位 */
} HSM_REQ_HEAD;
#pragma pack(pop)

/* 协议识别配置参数 */
typedef struct protocol_config {
    uint32_t max_packet_size;      /* 最大数据包大小 */
    uint16_t min_action;           /* 自定义协议最小action值 */
    uint16_t max_action;           /* 自定义协议最大action值 */
    uint8_t  strict_asn1_check;    /* 是否启用严格ASN.1检查 */
} PROTOCOL_CONFIG;

/**
 * @brief 协议识别主函数
 * @param data 数据包指针
 * @param data_len 数据包长度
 * @return PROTOCOL_ASN1(0) | PROTOCOL_CUSTOM(1) | PROTOCOL_UNKNOWN(-1)
 */
int identify_protocol(const unsigned char* data, size_t data_len);

/**
 * @brief 带配置参数的协议识别函数
 * @param data 数据包指针
 * @param data_len 数据包长度
 * @param config 识别配置参数
 * @return PROTOCOL_ASN1(0) | PROTOCOL_CUSTOM(1) | PROTOCOL_UNKNOWN(-1)
 */
int identify_protocol_ex(const unsigned char* data, size_t data_len, 
                        const PROTOCOL_CONFIG* config);

/**
 * @brief 检查是否为ASN.1 DER编码
 * @param data 数据包指针
 * @param data_len 数据包长度
 * @return 1表示是ASN.1，0表示不是
 */
int is_asn1_der_format(const unsigned char* data, size_t data_len);

/**
 * @brief 检查是否为自定义HSM协议
 * @param data 数据包指针
 * @param data_len 数据包长度
 * @return 1表示是HSM协议，0表示不是
 */
int is_custom_hsm_format(const unsigned char* data, size_t data_len);

/**
 * @brief 验证ASN.1长度编码的合理性
 * @param data 数据包指针
 * @param data_len 数据包长度
 * @param content_len 返回内容长度
 * @return 1表示合理，0表示不合理
 */
int validate_asn1_length(const unsigned char* data, size_t data_len, 
                        size_t* content_len);

/**
 * @brief 计算简单校验和（用于HSM协议验证）
 * @param data 数据指针
 * @param len 数据长度
 * @return 校验和值
 */
uint32_t calculate_simple_checksum(const unsigned char* data, size_t len);

/**
 * @brief 获取协议类型字符串描述
 * @param protocol_type 协议类型
 * @return 协议描述字符串
 */
const char* get_protocol_name(int protocol_type);

#ifdef __cplusplus
}
#endif

#endif /* PROTOCOL_IDENTIFIER_H */
