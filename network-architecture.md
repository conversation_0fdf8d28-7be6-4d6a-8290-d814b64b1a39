# 网络架构文档

## 概述

本文档描述了基于Open vSwitch (OVS)的虚拟化网络架构，包括物理连接、逻辑架构和数据流。

## 1. 物理连接架构

### 物理拓扑

```mermaid
graph TD
    A[物理交换机<br/>Cisco/HPE] -->|以太网线<br/>eth1 Trunk| B[服务器物理机]
    B --> C[物理网卡 eth0]
    C --> D[Open vSwitch<br/>ovs-br0]
    D --> E[虚拟端口 vnet1<br/>VLAN 10]
    D --> F[虚拟端口 vnet2<br/>VLAN 20]
    E --> G[VM1<br/>VLAN 10]
    F --> H[VM2<br/>VLAN 20]
    
    style A fill:#ff9999
    style B fill:#99ccff
    style D fill:#99ff99
    style G fill:#ffcc99
    style H fill:#ffcc99
```

### 设备层级结构

```
[ 物理交换机 (Cisco/HPE) ]
       |
       | (以太网线, 端口eth1配置为Trunk)
       |
[ 服务器 (物理机) ]
├── 物理网卡: eth0 (连接交换机eth1)
├── Open vSwitch (软件交换机)
│   ├── 虚拟网桥: ovs-br0 (绑定eth0)
│   ├── 虚拟端口: vnet1 (VLAN 10)
│   └── 虚拟端口: vnet2 (VLAN 20)
└── 虚拟机
    ├── VM1 (网卡绑定vnet1, VLAN 10)
    └── VM2 (网卡绑定vnet2, VLAN 20)
```

### 关键点

- 物理交换机与服务器通过 **eth0 ↔ eth1** 直连，线缆需支持 VLAN Tagging（如 Cat6）
- OVS 作为虚拟交换机在服务器内部运行，通过 ovs-br0 整合物理和虚拟网络

## 2. 逻辑架构图

### 网络层次结构

```mermaid
graph TB
    subgraph "物理交换机"
        PS[Trunk: VLAN 10,20,30<br/>端口 eth1]
    end
    
    subgraph "服务器"
        subgraph "Open vSwitch (OVS)"
            BR[ovs-br0<br/>绑定eth0]
            VNET1[vnet1<br/>tag=10]
            VNET2[vnet2<br/>tag=20]
            BR --- VNET1
            BR --- VNET2
        end
        
        subgraph "虚拟机"
            VM1[VM1<br/>VLAN 10]
            VM2[VM2<br/>VLAN 20]
        end
        
        VNET1 -.-> VM1
        VNET2 -.-> VM2
    end
    
    PS -->|eth1 Trunk| BR
    
    style PS fill:#ff9999
    style BR fill:#99ff99
    style VNET1 fill:#ffff99
    style VNET2 fill:#ffff99
    style VM1 fill:#ffcc99
    style VM2 fill:#ffcc99
```

### ASCII 架构图

```
+-----------------------------+
|       物理交换机            |
| (Trunk: VLAN 10,20,30)      |
+-------------+---------------+
              |
              | eth1 (Trunk)
+-------------+---------------+
|        服务器               |
| +-------------------------+ |
| |    Open vSwitch (OVS)    | |
| | +---------------------+  | |
| | |      ovs-br0        |  | |
| | |  (绑定eth0)         |  | |
| | +----------+----------+  | |
| |            |             | |
| |   +--------+---------+   | |
| |   | vnet1 (tag=10)   |   | |
| |   +------------------+   | |
| |   | vnet2 (tag=20)   |   | |
| |   +------------------+   | |
| +-------------------------+ |
| +-------------------------+ |
| |        虚拟机            | |
| |  +--------+  +--------+  | |
| |  | VM1    |  | VM2    |  | |
| |  | VLAN 10|  | VLAN 20|  | |
| |  +--------+  +--------+  | |
| +-------------------------+ |
+-----------------------------+
```

### 关键注释

- **红色箭头**：物理连接（eth0 ↔ eth1）
- **蓝色虚线**：OVS 内部虚拟端口与虚拟机的绑定关系
- **VLAN 标签**：数据包从 VM 到 OVS 时会被打上对应 VLAN Tag（如 vnet1 → tag=10）

## 3. 数据流示例

### VM1 到外部网络的数据流

```mermaid
flowchart LR
    A[VM1<br/>VLAN 10] --> B[vnet1<br/>OVS添加VLAN 10 Tag]
    B --> C[ovs-br0]
    C --> D[eth0<br/>Trunk透传Tag]
    D --> E[物理交换机eth1]
    E --> F[交换机根据VLAN 10<br/>路由到目标]
    
    style A fill:#ffcc99
    style B fill:#ffff99
    style C fill:#99ff99
    style D fill:#99ccff
    style E fill:#ff9999
    style F fill:#cccccc
```

### 数据流步骤

```
VM1 (VLAN 10) 
 → vnet1 (OVS添加VLAN 10 Tag) 
 → ovs-br0 
 → eth0 (Trunk透传Tag) 
 → 物理交换机eth1 
 → 交换机根据VLAN 10路由到目标
```

## 4. 配置要点

### 物理层配置
- 使用支持VLAN Tagging的网线（Cat6或更高）
- 物理交换机端口eth1配置为Trunk模式
- 允许VLAN 10, 20, 30通过

### OVS配置
- 创建虚拟网桥ovs-br0
- 将物理网卡eth0绑定到ovs-br0
- 创建虚拟端口vnet1和vnet2
- 配置相应的VLAN标签

### 虚拟机配置
- VM1网卡绑定到vnet1，配置VLAN 10
- VM2网卡绑定到vnet2，配置VLAN 20

## 5. 网络隔离

通过VLAN技术实现网络隔离：
- VLAN 10：VM1专用网络
- VLAN 20：VM2专用网络
- VLAN 30：预留用于扩展

不同VLAN之间的通信需要通过路由器或三层交换机进行路由。

sequenceDiagram
    participant 管理员
    participant 服务器
    participant OVS服务
    管理员->>服务器: 执行ovs-vsctl命令
    服务器->>OVS服务: 创建网桥ovs-br0
    OVS服务-->>服务器: 返回成功状态
    管理员->>服务器: 绑定物理网卡eth0到ovs-br0
    服务器->>OVS服务: 添加eth0端口并设置trunks
    OVS服务-->>服务器: 端口绑定成功 
