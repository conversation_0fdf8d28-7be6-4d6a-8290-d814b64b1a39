<?xml version="1.0" encoding="UTF-8"?>
<mxfile host="app.diagrams.net" modified="2025-07-29T00:00:00.000Z" agent="Augment Agent" etag="single-host-etcd" version="24.7.7">
  <diagram name="单宿主机etcd集群OVS VLAN网络架构" id="single-host-etcd-cluster">
    <mxGraphModel dx="1600" dy="1200" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1400" pageHeight="1800" math="0" shadow="0">
      <root>
        <mxCell id="0"/>
        <mxCell id="1" parent="0"/>

        <!-- ========== 架构总览 ========== -->
        <mxCell id="architecture-title" value="单宿主机etcd集群 - OVS VLAN网络架构" style="text;html=1;strokeColor=#6c8ebf;fillColor=#dae8fc;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=20;fontStyle=1;strokeWidth=3;" vertex="1" parent="1">
          <mxGeometry x="50" y="20" width="1300" height="50" as="geometry"/>
        </mxCell>

        <!-- 架构说明 -->
        <mxCell id="architecture-description" value="&lt;b&gt;单宿主机etcd集群特点:&lt;/b&gt;&lt;br&gt;• 一台物理宿主机运行3个etcd虚拟机&lt;br&gt;• OVS虚拟交换机提供网络连接&lt;br&gt;• 简化网络设计：2个网口配置&lt;br&gt;• VIP负载均衡器部署在etcd-node-1&lt;br&gt;• 集群内部通信使用业务网络" style="shape=callout;whiteSpace=wrap;html=1;perimeter=calloutPerimeter;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=12;align=left;" vertex="1" parent="1">
          <mxGeometry x="50" y="90" width="400" height="120" as="geometry"/>
        </mxCell>

        <!-- ========== Trunk模式配置 ========== -->
        <mxCell id="trunk-mode-title" value="Trunk模式 - 单宿主机etcd集群网络拓扑" style="text;html=1;strokeColor=#6c8ebf;fillColor=#dae8fc;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=18;fontStyle=1;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="50" y="240" width="1300" height="40" as="geometry"/>
        </mxCell>

        <!-- 物理交换机 -->
        <mxCell id="physical-switch-trunk" value="物理交换机&lt;br&gt;&lt;b&gt;Trunk端口配置&lt;/b&gt;&lt;br&gt;允许VLAN: 100,200,300&lt;br&gt;Native VLAN: 1&lt;br&gt;端口: GigabitEthernet0/1" style="shape=rectangle;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="600" y="300" width="200" height="100" as="geometry"/>
        </mxCell>

        <!-- 交换机配置 -->
        <mxCell id="switch-trunk-config" value="&lt;b&gt;交换机Trunk配置:&lt;/b&gt;&lt;br&gt;interface GigabitEthernet0/1&lt;br&gt; switchport mode trunk&lt;br&gt; switchport trunk allowed vlan 100,300&lt;br&gt; switchport trunk native vlan 1&lt;br&gt; spanning-tree portfast trunk&lt;br&gt;&lt;br&gt;&lt;b&gt;简化VLAN规划:&lt;/b&gt;&lt;br&gt;• VLAN 100: 业务网络 (etcd客户端+集群通信)&lt;br&gt;• VLAN 300: 管理网络 (SSH+监控)" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=10;align=left;" vertex="1" parent="1">
          <mxGeometry x="850" y="300" width="320" height="140" as="geometry"/>
        </mxCell>

        <!-- 宿主机 -->
        <mxCell id="host-server-trunk" value="物理宿主机 (CentOS/Ubuntu)&lt;br&gt;&lt;b&gt;OVS网桥: br0&lt;/b&gt;&lt;br&gt;模式: Trunk&lt;br&gt;物理网卡: eth0&lt;br&gt;CPU: 8核, 内存: 32GB" style="shape=rectangle;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="600" y="450" width="200" height="120" as="geometry"/>
        </mxCell>

        <!-- OVS Trunk配置 -->
        <mxCell id="ovs-trunk-config" value="&lt;b&gt;OVS Trunk模式配置 (简化版):&lt;/b&gt;&lt;br&gt;# 创建OVS网桥&lt;br&gt;ovs-vsctl add-br br0&lt;br&gt;ovs-vsctl add-port br0 eth0&lt;br&gt;ovs-vsctl set port eth0 trunk=100,300&lt;br&gt;&lt;br&gt;# 创建VM端口 (Trunk模式)&lt;br&gt;ovs-vsctl add-port br0 vnet0 trunk=100,300&lt;br&gt;ovs-vsctl add-port br0 vnet1 trunk=100,300&lt;br&gt;ovs-vsctl add-port br0 vnet2 trunk=100,300&lt;br&gt;&lt;br&gt;# 查看配置&lt;br&gt;ovs-vsctl show&lt;br&gt;ovs-ofctl dump-flows br0" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f8cecc;strokeColor=#b85450;fontSize=9;align=left;" vertex="1" parent="1">
          <mxGeometry x="50" y="450" width="350" height="180" as="geometry"/>
        </mxCell>

        <!-- etcd虚拟机集群 -->
        <mxCell id="etcd-vm1-trunk" value="etcd-node-1 + HAProxy&lt;br&gt;&lt;b&gt;双网口配置&lt;/b&gt;&lt;br&gt;eth0.100: **************/24 (业务)&lt;br&gt;eth0.300: 192.168.300.11/24 (管理)&lt;br&gt;VIP: *************** (绑定eth0.100)&lt;br&gt;etcd端口: 2379,2380&lt;br&gt;HAProxy端口: 2379 (VIP)" style="shape=rectangle;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="200" y="650" width="200" height="140" as="geometry"/>
        </mxCell>

        <mxCell id="etcd-vm2-trunk" value="etcd-node-2&lt;br&gt;&lt;b&gt;双网口配置&lt;/b&gt;&lt;br&gt;eth0.100: **************/24 (业务)&lt;br&gt;eth0.300: 192.168.300.12/24 (管理)&lt;br&gt;etcd端口: 2379,2380&lt;br&gt;集群通信: VLAN 100" style="shape=rectangle;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="600" y="650" width="180" height="120" as="geometry"/>
        </mxCell>

        <mxCell id="etcd-vm3-trunk" value="etcd-node-3&lt;br&gt;&lt;b&gt;双网口配置&lt;/b&gt;&lt;br&gt;eth0.100: **************/24 (业务)&lt;br&gt;eth0.300: 192.168.300.13/24 (管理)&lt;br&gt;etcd端口: 2379,2380&lt;br&gt;集群通信: VLAN 100" style="shape=rectangle;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="1000" y="650" width="180" height="120" as="geometry"/>
        </mxCell>

        <!-- VLAN网络说明 -->
        <mxCell id="vlan-explanation-trunk" value="&lt;b&gt;简化VLAN网络规划:&lt;/b&gt;&lt;br&gt;&lt;br&gt;&lt;b&gt;VLAN 100 (业务网络):&lt;/b&gt;&lt;br&gt;• etcd客户端访问 (端口2379)&lt;br&gt;• etcd集群内部通信 (端口2380)&lt;br&gt;• HAProxy负载均衡&lt;br&gt;• 网段: 192.168.100.0/24&lt;br&gt;&lt;br&gt;&lt;b&gt;VLAN 300 (管理网络):&lt;/b&gt;&lt;br&gt;• SSH管理访问 (端口22)&lt;br&gt;• 监控数据收集 (端口9100)&lt;br&gt;• 网段: 192.168.300.0/24&lt;br&gt;&lt;br&gt;&lt;b&gt;设计优势:&lt;/b&gt;&lt;br&gt;• 简化网络配置&lt;br&gt;• 减少VLAN数量&lt;br&gt;• 降低管理复杂度" style="shape=callout;whiteSpace=wrap;html=1;perimeter=calloutPerimeter;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=10;align=left;" vertex="1" parent="1">
          <mxGeometry x="850" y="450" width="300" height="220" as="geometry"/>
        </mxCell>

        <!-- VM网络配置详情 -->
        <mxCell id="vm-network-config-trunk" value="&lt;b&gt;VM网络接口配置 (简化双网口):&lt;/b&gt;&lt;br&gt;&lt;br&gt;# 配置VLAN接口 (以node-1为例)&lt;br&gt;ip link add link eth0 name eth0.100 type vlan id 100&lt;br&gt;ip link add link eth0 name eth0.300 type vlan id 300&lt;br&gt;&lt;br&gt;# 配置IP地址&lt;br&gt;ip addr add **************/24 dev eth0.100&lt;br&gt;ip addr add 192.168.300.11/24 dev eth0.300&lt;br&gt;&lt;br&gt;# 启用接口&lt;br&gt;ip link set eth0.100 up&lt;br&gt;ip link set eth0.300 up&lt;br&gt;&lt;br&gt;# 配置VIP (仅在node-1上)&lt;br&gt;ip addr add ***************/24 dev eth0.100&lt;br&gt;&lt;br&gt;# 配置路由&lt;br&gt;ip route add default via ************* dev eth0.100&lt;br&gt;&lt;br&gt;# 安装HAProxy (仅在node-1上)&lt;br&gt;apt-get install haproxy" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=9;align=left;" vertex="1" parent="1">
          <mxGeometry x="50" y="800" width="400" height="240" as="geometry"/>
        </mxCell>

        <!-- HAProxy配置说明 -->
        <mxCell id="haproxy-deployment-explanation" value="&lt;b&gt;HAProxy部署在etcd-node-1:&lt;/b&gt;&lt;br&gt;&lt;br&gt;&lt;b&gt;部署优势:&lt;/b&gt;&lt;br&gt;• 节省VM资源&lt;br&gt;• 配置简化&lt;br&gt;• 适合小规模部署&lt;br&gt;&lt;br&gt;&lt;b&gt;注意事项:&lt;/b&gt;&lt;br&gt;• etcd-node-1故障会影响VIP&lt;br&gt;• 需要监控节点健康状态&lt;br&gt;• 建议配置自动故障转移&lt;br&gt;&lt;br&gt;&lt;b&gt;VIP配置:&lt;/b&gt;&lt;br&gt;• VIP: ***************&lt;br&gt;• 绑定到eth0.100接口&lt;br&gt;• 后端: 本机+其他节点" style="shape=callout;whiteSpace=wrap;html=1;perimeter=calloutPerimeter;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=10;align=left;" vertex="1" parent="1">
          <mxGeometry x="850" y="700" width="300" height="200" as="geometry"/>
        </mxCell>

        <!-- VIP配置详情 -->
        <mxCell id="vip-config-trunk" value="&lt;b&gt;HAProxy配置 (部署在etcd-node-1):&lt;/b&gt;&lt;br&gt;&lt;br&gt;# 1. 配置VIP&lt;br&gt;ip addr add ***************/24 dev eth0.100&lt;br&gt;&lt;br&gt;# 2. HAProxy配置文件&lt;br&gt;global&lt;br&gt;    daemon&lt;br&gt;&lt;br&gt;defaults&lt;br&gt;    mode tcp&lt;br&gt;    timeout connect 5000ms&lt;br&gt;    timeout client 50000ms&lt;br&gt;    timeout server 50000ms&lt;br&gt;&lt;br&gt;frontend etcd_frontend&lt;br&gt;    bind ***************:2379&lt;br&gt;    default_backend etcd_backend&lt;br&gt;&lt;br&gt;backend etcd_backend&lt;br&gt;    balance roundrobin&lt;br&gt;    option tcp-check&lt;br&gt;    server etcd1 127.0.0.1:2379 check&lt;br&gt;    server etcd2 **************:2379 check&lt;br&gt;    server etcd3 **************:2379 check&lt;br&gt;&lt;br&gt;# 客户端通过VIP访问&lt;br&gt;etcdctl --endpoints=https://***************:2379 get /test" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=8;align=left;" vertex="1" parent="1">
          <mxGeometry x="1200" y="650" width="350" height="300" as="geometry"/>
        </mxCell>

        <!-- etcd集群配置 -->
        <mxCell id="etcd-cluster-config-trunk" value="&lt;b&gt;etcd集群配置 (简化双网口):&lt;/b&gt;&lt;br&gt;&lt;br&gt;# etcd-node-1 配置&lt;br&gt;ETCD_NAME=etcd-node-1&lt;br&gt;ETCD_LISTEN_CLIENT_URLS=https://**************:2379&lt;br&gt;ETCD_LISTEN_PEER_URLS=https://**************:2380&lt;br&gt;ETCD_ADVERTISE_CLIENT_URLS=https://**************:2379&lt;br&gt;ETCD_INITIAL_ADVERTISE_PEER_URLS=https://**************:2380&lt;br&gt;&lt;br&gt;# 集群初始化 (使用业务网络通信)&lt;br&gt;ETCD_INITIAL_CLUSTER=etcd-node-1=https://**************:2380,etcd-node-2=https://**************:2380,etcd-node-3=https://**************:2380&lt;br&gt;&lt;br&gt;# 集群状态检查&lt;br&gt;etcdctl --endpoints=https://**************:2379 endpoint health&lt;br&gt;etcdctl --endpoints=https://**************:2379 member list&lt;br&gt;&lt;br&gt;# 通过VIP访问集群 (推荐)&lt;br&gt;etcdctl --endpoints=https://***************:2379 get /test&lt;br&gt;etcdctl --endpoints=https://***************:2379 put /test &quot;value&quot;&lt;br&gt;&lt;br&gt;&lt;b&gt;网络简化优势:&lt;/b&gt;&lt;br&gt;• 客户端访问和集群通信使用同一网络&lt;br&gt;• 减少网络配置复杂度&lt;br&gt;• 降低故障排查难度" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=9;align=left;" vertex="1" parent="1">
          <mxGeometry x="500" y="800" width="600" height="280" as="geometry"/>
        </mxCell>

        <!-- HAProxy负载均衡连接线 (从etcd-node-1到其他节点) -->
        <mxCell id="haproxy-conn-1" style="endArrow=classic;startArrow=classic;edgeStyle=orthogonalEdgeStyle;strokeWidth=3;strokeColor=#b85450;" edge="1" parent="1" source="etcd-vm1-trunk" target="etcd-vm2-trunk">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        <mxCell id="haproxy-conn-2" style="endArrow=classic;startArrow=classic;edgeStyle=orthogonalEdgeStyle;strokeWidth=3;strokeColor=#b85450;" edge="1" parent="1" source="etcd-vm1-trunk" target="etcd-vm3-trunk">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <!-- 连接线 - Trunk模式 -->
        <mxCell id="trunk-conn-switch-host" style="endArrow=classic;edgeStyle=orthogonalEdgeStyle;strokeWidth=3;strokeColor=#6c8ebf;" edge="1" parent="1" source="physical-switch-trunk" target="host-server-trunk">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <mxCell id="trunk-conn-host-vm1" style="endArrow=classic;edgeStyle=orthogonalEdgeStyle;strokeWidth=2;strokeColor=#9673a6;" edge="1" parent="1" source="host-server-trunk" target="etcd-vm1-trunk">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <mxCell id="trunk-conn-host-vm2" style="endArrow=classic;edgeStyle=orthogonalEdgeStyle;strokeWidth=2;strokeColor=#9673a6;" edge="1" parent="1" source="host-server-trunk" target="etcd-vm2-trunk">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <mxCell id="trunk-conn-host-vm3" style="endArrow=classic;edgeStyle=orthogonalEdgeStyle;strokeWidth=2;strokeColor=#9673a6;" edge="1" parent="1" source="host-server-trunk" target="etcd-vm3-trunk">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <!-- etcd集群内部通信 -->
        <mxCell id="etcd-comm-1-2" style="endArrow=classic;startArrow=classic;edgeStyle=orthogonalEdgeStyle;strokeWidth=2;strokeColor=#82b366;strokeDashArray=5 5;" edge="1" parent="1" source="etcd-vm1-trunk" target="etcd-vm2-trunk">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <mxCell id="etcd-comm-2-3" style="endArrow=classic;startArrow=classic;edgeStyle=orthogonalEdgeStyle;strokeWidth=2;strokeColor=#82b366;strokeDashArray=5 5;" edge="1" parent="1" source="etcd-vm2-trunk" target="etcd-vm3-trunk">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <mxCell id="etcd-comm-1-3" style="endArrow=classic;startArrow=classic;edgeStyle=orthogonalEdgeStyle;strokeWidth=2;strokeColor=#82b366;strokeDashArray=5 5;" edge="1" parent="1" source="etcd-vm1-trunk" target="etcd-vm3-trunk">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="290" y="780"/>
              <mxPoint x="1090" y="780"/>
            </Array>
          </mxGeometry>
        </mxCell>

        <!-- ========== Access模式配置 ========== -->
        <mxCell id="access-mode-title" value="Access模式 - 单宿主机etcd集群网络拓扑" style="text;html=1;strokeColor=#6c8ebf;fillColor=#dae8fc;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=18;fontStyle=1;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="50" y="1080" width="1300" height="40" as="geometry"/>
        </mxCell>

        <!-- 物理交换机 Access模式 -->
        <mxCell id="physical-switch-access" value="物理交换机&lt;br&gt;&lt;b&gt;Access端口配置&lt;/b&gt;&lt;br&gt;Access VLAN: 100&lt;br&gt;端口模式: Access&lt;br&gt;端口: GigabitEthernet0/2" style="shape=rectangle;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="600" y="1140" width="200" height="100" as="geometry"/>
        </mxCell>

        <!-- 交换机Access配置 -->
        <mxCell id="switch-access-config" value="&lt;b&gt;交换机Access配置 (简化版):&lt;/b&gt;&lt;br&gt;# 业务网络端口&lt;br&gt;interface GigabitEthernet0/2&lt;br&gt; switchport mode access&lt;br&gt; switchport access vlan 100&lt;br&gt; spanning-tree portfast&lt;br&gt;&lt;br&gt;# 管理网络端口&lt;br&gt;interface GigabitEthernet0/3&lt;br&gt; switchport mode access&lt;br&gt; switchport access vlan 300&lt;br&gt;&lt;br&gt;&lt;b&gt;简化设计:&lt;/b&gt;&lt;br&gt;• 只使用2个VLAN&lt;br&gt;• 集群通信使用业务网络" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=10;align=left;" vertex="1" parent="1">
          <mxGeometry x="850" y="1140" width="320" height="180" as="geometry"/>
        </mxCell>

        <!-- 宿主机 Access模式 -->
        <mxCell id="host-server-access" value="物理宿主机 (CentOS/Ubuntu)&lt;br&gt;&lt;b&gt;双OVS网桥&lt;/b&gt;&lt;br&gt;br-business: VLAN 100&lt;br&gt;br-mgmt: VLAN 300&lt;br&gt;简化配置" style="shape=rectangle;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=12;" vertex="1" parent="1">
          <mxGeometry x="600" y="1290" width="200" height="120" as="geometry"/>
        </mxCell>

        <!-- OVS Access配置 -->
        <mxCell id="ovs-access-config" value="&lt;b&gt;OVS Access模式配置 (简化版):&lt;/b&gt;&lt;br&gt;&lt;br&gt;# 创建业务网络网桥 (VLAN 100)&lt;br&gt;ovs-vsctl add-br br-business&lt;br&gt;ovs-vsctl add-port br-business eth0&lt;br&gt;ovs-vsctl set port br-business tag=100&lt;br&gt;&lt;br&gt;# 创建管理网络网桥 (VLAN 300)&lt;br&gt;ovs-vsctl add-br br-mgmt&lt;br&gt;ovs-vsctl add-port br-mgmt eth1&lt;br&gt;ovs-vsctl set port br-mgmt tag=300&lt;br&gt;&lt;br&gt;# 添加VM端口到对应网桥&lt;br&gt;ovs-vsctl add-port br-business vnet0-eth0&lt;br&gt;ovs-vsctl add-port br-mgmt vnet0-eth1&lt;br&gt;ovs-vsctl add-port br-business vnet1-eth0&lt;br&gt;ovs-vsctl add-port br-mgmt vnet1-eth1&lt;br&gt;ovs-vsctl add-port br-business vnet2-eth0&lt;br&gt;ovs-vsctl add-port br-mgmt vnet2-eth1&lt;br&gt;&lt;br&gt;&lt;b&gt;简化优势:&lt;/b&gt;&lt;br&gt;• 只需2个网桥&lt;br&gt;• VM只需2个网卡&lt;br&gt;• 配置更简单" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f8cecc;strokeColor=#b85450;fontSize=9;align=left;" vertex="1" parent="1">
          <mxGeometry x="50" y="1290" width="400" height="280" as="geometry"/>
        </mxCell>

        <!-- etcd虚拟机 Access模式 -->
        <mxCell id="etcd-vm1-access" value="etcd-node-1 + HAProxy&lt;br&gt;&lt;b&gt;双网卡配置&lt;/b&gt;&lt;br&gt;eth0: **************/24 (业务)&lt;br&gt;eth1: 192.168.300.11/24 (管理)&lt;br&gt;VIP: *************** (绑定eth0)&lt;br&gt;etcd端口: 2379,2380&lt;br&gt;HAProxy端口: 2379 (VIP)" style="shape=rectangle;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="200" y="1490" width="200" height="140" as="geometry"/>
        </mxCell>

        <mxCell id="etcd-vm2-access" value="etcd-node-2&lt;br&gt;&lt;b&gt;双网卡配置&lt;/b&gt;&lt;br&gt;eth0: **************/24 (业务)&lt;br&gt;eth1: 192.168.300.12/24 (管理)&lt;br&gt;etcd端口: 2379,2380&lt;br&gt;集群通信: 业务网络" style="shape=rectangle;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="600" y="1490" width="180" height="120" as="geometry"/>
        </mxCell>

        <mxCell id="etcd-vm3-access" value="etcd-node-3&lt;br&gt;&lt;b&gt;双网卡配置&lt;/b&gt;&lt;br&gt;eth0: **************/24 (业务)&lt;br&gt;eth1: 192.168.300.13/24 (管理)&lt;br&gt;etcd端口: 2379,2380&lt;br&gt;集群通信: 业务网络" style="shape=rectangle;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11;" vertex="1" parent="1">
          <mxGeometry x="1000" y="1490" width="180" height="120" as="geometry"/>
        </mxCell>

        <!-- Access模式网络说明 -->
        <mxCell id="access-network-explanation" value="&lt;b&gt;Access模式网络特点 (简化版):&lt;/b&gt;&lt;br&gt;&lt;br&gt;&lt;b&gt;优势:&lt;/b&gt;&lt;br&gt;• VM配置简化，无需VLAN标签&lt;br&gt;• 网络隔离更彻底&lt;br&gt;• 故障排查更容易&lt;br&gt;• 只需2个网口，降低复杂度&lt;br&gt;&lt;br&gt;&lt;b&gt;简化设计:&lt;/b&gt;&lt;br&gt;• 业务网络承载客户端访问和集群通信&lt;br&gt;• 管理网络独立，提高安全性&lt;br&gt;• 减少网桥数量，简化配置&lt;br&gt;&lt;br&gt;&lt;b&gt;适用场景:&lt;/b&gt;&lt;br&gt;• 中小规模部署&lt;br&gt;• 网络配置要求简单&lt;br&gt;• 资源有限的环境" style="shape=callout;whiteSpace=wrap;html=1;perimeter=calloutPerimeter;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=10;align=left;" vertex="1" parent="1">
          <mxGeometry x="850" y="1320" width="300" height="220" as="geometry"/>
        </mxCell>

        <!-- VM网络配置 Access模式 -->
        <mxCell id="vm-network-config-access" value="&lt;b&gt;VM网络配置 (Access模式简化版):&lt;/b&gt;&lt;br&gt;&lt;br&gt;# 配置业务网络接口 (以node-1为例)&lt;br&gt;ip addr add **************/24 dev eth0&lt;br&gt;ip link set eth0 up&lt;br&gt;&lt;br&gt;# 配置管理网络接口&lt;br&gt;ip addr add 192.168.300.11/24 dev eth1&lt;br&gt;ip link set eth1 up&lt;br&gt;&lt;br&gt;# 配置VIP (仅在node-1上)&lt;br&gt;ip addr add ***************/24 dev eth0&lt;br&gt;&lt;br&gt;# 配置路由&lt;br&gt;ip route add default via ************* dev eth0&lt;br&gt;&lt;br&gt;# 安装HAProxy (仅在node-1上)&lt;br&gt;apt-get install haproxy&lt;br&gt;&lt;br&gt;# 网络接口检查&lt;br&gt;ip addr show&lt;br&gt;ip route show" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=9;align=left;" vertex="1" parent="1">
          <mxGeometry x="50" y="1640" width="400" height="240" as="geometry"/>
        </mxCell>



        <!-- etcd集群配置 Access模式 -->
        <mxCell id="etcd-cluster-config-access" value="&lt;b&gt;etcd集群配置 (Access模式简化版):&lt;/b&gt;&lt;br&gt;&lt;br&gt;# etcd-node-1 配置&lt;br&gt;ETCD_NAME=etcd-node-1&lt;br&gt;ETCD_LISTEN_CLIENT_URLS=https://**************:2379&lt;br&gt;ETCD_LISTEN_PEER_URLS=https://**************:2380&lt;br&gt;ETCD_ADVERTISE_CLIENT_URLS=https://**************:2379&lt;br&gt;ETCD_INITIAL_ADVERTISE_PEER_URLS=https://**************:2380&lt;br&gt;&lt;br&gt;# 集群初始化 (使用业务网络)&lt;br&gt;ETCD_INITIAL_CLUSTER=etcd-node-1=https://**************:2380,etcd-node-2=https://**************:2380,etcd-node-3=https://**************:2380&lt;br&gt;&lt;br&gt;# 简化网络配置&lt;br&gt;# 客户端访问: eth0 (VLAN 100)&lt;br&gt;# 集群通信: eth0 (VLAN 100) - 共用业务网络&lt;br&gt;# 管理监控: eth1 (VLAN 300)&lt;br&gt;&lt;br&gt;# 通过VIP访问集群 (推荐)&lt;br&gt;etcdctl --endpoints=https://***************:2379 get /test&lt;br&gt;etcdctl --endpoints=https://***************:2379 put /test &quot;value&quot;&lt;br&gt;&lt;br&gt;# HAProxy配置 (在node-1上)&lt;br&gt;backend etcd_backend&lt;br&gt;    server etcd1 127.0.0.1:2379 check&lt;br&gt;    server etcd2 **************:2379 check&lt;br&gt;    server etcd3 **************:2379 check&lt;br&gt;&lt;br&gt;# 防火墙规则示例&lt;br&gt;iptables -A INPUT -i eth0 -p tcp --dport 2379 -j ACCEPT&lt;br&gt;iptables -A INPUT -i eth0 -p tcp --dport 2380 -j ACCEPT" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=9;align=left;" vertex="1" parent="1">
          <mxGeometry x="500" y="1640" width="600" height="320" as="geometry"/>
        </mxCell>

        <!-- HAProxy负载均衡连接线 Access模式 (从etcd-node-1到其他节点) -->
        <mxCell id="haproxy-access-conn-1" style="endArrow=classic;startArrow=classic;edgeStyle=orthogonalEdgeStyle;strokeWidth=3;strokeColor=#b85450;" edge="1" parent="1" source="etcd-vm1-access" target="etcd-vm2-access">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>
        <mxCell id="haproxy-access-conn-2" style="endArrow=classic;startArrow=classic;edgeStyle=orthogonalEdgeStyle;strokeWidth=3;strokeColor=#b85450;" edge="1" parent="1" source="etcd-vm1-access" target="etcd-vm3-access">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <!-- 连接线 - Access模式 -->
        <mxCell id="access-conn-switch-host" style="endArrow=classic;edgeStyle=orthogonalEdgeStyle;strokeWidth=3;strokeColor=#6c8ebf;" edge="1" parent="1" source="physical-switch-access" target="host-server-access">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <mxCell id="access-conn-host-vm1" style="endArrow=classic;edgeStyle=orthogonalEdgeStyle;strokeWidth=2;strokeColor=#9673a6;" edge="1" parent="1" source="host-server-access" target="etcd-vm1-access">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <mxCell id="access-conn-host-vm2" style="endArrow=classic;edgeStyle=orthogonalEdgeStyle;strokeWidth=2;strokeColor=#9673a6;" edge="1" parent="1" source="host-server-access" target="etcd-vm2-access">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <mxCell id="access-conn-host-vm3" style="endArrow=classic;edgeStyle=orthogonalEdgeStyle;strokeWidth=2;strokeColor=#9673a6;" edge="1" parent="1" source="host-server-access" target="etcd-vm3-access">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <!-- etcd集群内部通信 Access模式 -->
        <mxCell id="access-etcd-comm-1-2" style="endArrow=classic;startArrow=classic;edgeStyle=orthogonalEdgeStyle;strokeWidth=2;strokeColor=#82b366;strokeDashArray=5 5;" edge="1" parent="1" source="etcd-vm1-access" target="etcd-vm2-access">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <mxCell id="access-etcd-comm-2-3" style="endArrow=classic;startArrow=classic;edgeStyle=orthogonalEdgeStyle;strokeWidth=2;strokeColor=#82b366;strokeDashArray=5 5;" edge="1" parent="1" source="etcd-vm2-access" target="etcd-vm3-access">
          <mxGeometry relative="1" as="geometry"/>
        </mxCell>

        <mxCell id="access-etcd-comm-1-3" style="endArrow=classic;startArrow=classic;edgeStyle=orthogonalEdgeStyle;strokeWidth=2;strokeColor=#82b366;strokeDashArray=5 5;" edge="1" parent="1" source="etcd-vm1-access" target="etcd-vm3-access">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="290" y="1620"/>
              <mxPoint x="1090" y="1620"/>
            </Array>
          </mxGeometry>
        </mxCell>

        <!-- ========== 模式对比和最佳实践 ========== -->
        <mxCell id="comparison-title" value="Trunk vs Access模式对比分析与最佳实践" style="text;html=1;strokeColor=#6c8ebf;fillColor=#dae8fc;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=18;fontStyle=1;strokeWidth=2;" vertex="1" parent="1">
          <mxGeometry x="50" y="1900" width="1300" height="40" as="geometry"/>
        </mxCell>

        <!-- 模式对比表格 -->
        <mxCell id="mode-comparison-table" value="&lt;table border=&quot;1&quot; style=&quot;border-collapse: collapse; width: 100%; font-size: 11px;&quot;&gt;&lt;tr&gt;&lt;th style=&quot;background-color: #3498db; color: white; padding: 8px;&quot;&gt;对比维度&lt;/th&gt;&lt;th style=&quot;background-color: #3498db; color: white; padding: 8px;&quot;&gt;Trunk模式 (简化版)&lt;/th&gt;&lt;th style=&quot;background-color: #3498db; color: white; padding: 8px;&quot;&gt;Access模式 (简化版)&lt;/th&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td style=&quot;padding: 6px;&quot;&gt;&lt;b&gt;VM网络接口&lt;/b&gt;&lt;/td&gt;&lt;td style=&quot;padding: 6px;&quot;&gt;单网卡双VLAN (eth0.100, eth0.300)&lt;/td&gt;&lt;td style=&quot;padding: 6px;&quot;&gt;双网卡单VLAN (eth0, eth1)&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td style=&quot;padding: 6px;&quot;&gt;&lt;b&gt;OVS配置&lt;/b&gt;&lt;/td&gt;&lt;td style=&quot;padding: 6px;&quot;&gt;单网桥，端口设置trunk=100,300&lt;/td&gt;&lt;td style=&quot;padding: 6px;&quot;&gt;双网桥 (br-business, br-mgmt)&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td style=&quot;padding: 6px;&quot;&gt;&lt;b&gt;VLAN数量&lt;/b&gt;&lt;/td&gt;&lt;td style=&quot;padding: 6px;&quot;&gt;2个VLAN (100业务, 300管理)&lt;/td&gt;&lt;td style=&quot;padding: 6px;&quot;&gt;2个VLAN (100业务, 300管理)&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td style=&quot;padding: 6px;&quot;&gt;&lt;b&gt;集群通信&lt;/b&gt;&lt;/td&gt;&lt;td style=&quot;padding: 6px;&quot;&gt;使用业务网络 (VLAN 100)&lt;/td&gt;&lt;td style=&quot;padding: 6px;&quot;&gt;使用业务网络 (VLAN 100)&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td style=&quot;padding: 6px;&quot;&gt;&lt;b&gt;VIP部署&lt;/b&gt;&lt;/td&gt;&lt;td style=&quot;padding: 6px;&quot;&gt;etcd-node-1 + HAProxy&lt;/td&gt;&lt;td style=&quot;padding: 6px;&quot;&gt;etcd-node-1 + HAProxy&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td style=&quot;padding: 6px;&quot;&gt;&lt;b&gt;配置复杂度&lt;/b&gt;&lt;/td&gt;&lt;td style=&quot;padding: 6px;&quot;&gt;VM内需配置VLAN接口&lt;/td&gt;&lt;td style=&quot;padding: 6px;&quot;&gt;VM配置简单，OVS稍复杂&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td style=&quot;padding: 6px;&quot;&gt;&lt;b&gt;适用场景&lt;/b&gt;&lt;/td&gt;&lt;td style=&quot;padding: 6px;&quot;&gt;网络接口有限，灵活配置&lt;/td&gt;&lt;td style=&quot;padding: 6px;&quot;&gt;简化VM配置，网络隔离&lt;/td&gt;&lt;/tr&gt;&lt;/table&gt;" style="text;html=1;strokeColor=#6c8ebf;fillColor=none;align=left;verticalAlign=top;whiteSpace=wrap;rounded=0;fontSize=10;" vertex="1" parent="1">
          <mxGeometry x="50" y="2000" width="800" height="180" as="geometry"/>
        </mxCell>

        <!-- VIP配置方案对比 -->
        <mxCell id="vip-configuration-comparison" value="&lt;b&gt;VIP部署方案 (etcd-node-1):&lt;/b&gt;&lt;br&gt;&lt;br&gt;&lt;b&gt;1. 部署位置:&lt;/b&gt;&lt;br&gt;• HAProxy部署在etcd-node-1上&lt;br&gt;• VIP绑定到业务网络接口&lt;br&gt;• 节省VM资源，配置简化&lt;br&gt;&lt;br&gt;&lt;b&gt;2. 配置特点:&lt;/b&gt;&lt;br&gt;• VIP: ***************&lt;br&gt;• 后端: 本机(127.0.0.1) + 其他节点&lt;br&gt;• 健康检查: TCP连接检测&lt;br&gt;&lt;br&gt;&lt;b&gt;3. 优势:&lt;/b&gt;&lt;br&gt;• 资源利用率高&lt;br&gt;• 配置管理简单&lt;br&gt;• 适合小规模部署&lt;br&gt;&lt;br&gt;&lt;b&gt;4. 注意事项:&lt;/b&gt;&lt;br&gt;• etcd-node-1故障影响VIP&lt;br&gt;• 建议配置监控告警&lt;br&gt;• 可考虑故障转移方案" style="shape=callout;whiteSpace=wrap;html=1;perimeter=calloutPerimeter;fillColor=#fff2cc;strokeColor=#d6b656;fontSize=10;align=left;" vertex="1" parent="1">
          <mxGeometry x="900" y="2000" width="400" height="280" as="geometry"/>
        </mxCell>

        <!-- etcd集群最佳实践 -->
        <mxCell id="etcd-best-practices" value="&lt;b&gt;etcd集群网络最佳实践:&lt;/b&gt;&lt;br&gt;&lt;br&gt;&lt;b&gt;1. 网络规划建议:&lt;/b&gt;&lt;br&gt;• 客户端访问网络 (VLAN 100): 低延迟要求&lt;br&gt;• 集群内部通信 (VLAN 200): 高带宽要求&lt;br&gt;• 管理监控网络 (VLAN 300): 安全隔离&lt;br&gt;&lt;br&gt;&lt;b&gt;2. VIP负载均衡配置:&lt;/b&gt;&lt;br&gt;• 使用HAProxy/Nginx提供VIP&lt;br&gt;• 配置健康检查机制&lt;br&gt;• 启用会话保持 (可选)&lt;br&gt;• 监控后端节点状态&lt;br&gt;&lt;br&gt;&lt;b&gt;3. 性能优化:&lt;/b&gt;&lt;br&gt;• 集群通信使用专用网络&lt;br&gt;• 启用网卡多队列&lt;br&gt;• 调整TCP缓冲区大小&lt;br&gt;• 使用SSD存储&lt;br&gt;&lt;br&gt;&lt;b&gt;4. 安全配置:&lt;/b&gt;&lt;br&gt;• 启用TLS加密&lt;br&gt;• 配置防火墙规则&lt;br&gt;• 限制网络访问权限&lt;br&gt;• 定期更新证书" style="shape=callout;whiteSpace=wrap;html=1;perimeter=calloutPerimeter;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=10;align=left;" vertex="1" parent="1">
          <mxGeometry x="900" y="2220" width="400" height="300" as="geometry"/>
        </mxCell>

        <!-- 部署脚本示例 -->
        <mxCell id="deployment-scripts" value="&lt;b&gt;自动化部署脚本:&lt;/b&gt;&lt;br&gt;&lt;br&gt;#!/bin/bash&lt;br&gt;# 单宿主机etcd集群部署脚本&lt;br&gt;&lt;br&gt;# 选择模式: trunk 或 access&lt;br&gt;MODE=${1:-trunk}&lt;br&gt;&lt;br&gt;if [ &quot;$MODE&quot; = &quot;trunk&quot; ]; then&lt;br&gt;    # Trunk模式配置&lt;br&gt;    ovs-vsctl add-br br0&lt;br&gt;    ovs-vsctl add-port br0 eth0&lt;br&gt;    ovs-vsctl set port eth0 trunk=100,200,300&lt;br&gt;    &lt;br&gt;    # 创建VM端口&lt;br&gt;    for i in {0..2}; do&lt;br&gt;        ovs-vsctl add-port br0 vnet$i trunk=100,200,300&lt;br&gt;    done&lt;br&gt;else&lt;br&gt;    # Access模式配置&lt;br&gt;    for vlan in 100 200 300; do&lt;br&gt;        ovs-vsctl add-br br-$vlan&lt;br&gt;        ovs-vsctl set port br-$vlan tag=$vlan&lt;br&gt;    done&lt;br&gt;fi&lt;br&gt;&lt;br&gt;echo &quot;OVS配置完成，模式: $MODE&quot;" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=9;align=left;" vertex="1" parent="1">
          <mxGeometry x="50" y="2200" width="400" height="240" as="geometry"/>
        </mxCell>

        <!-- 监控和故障排查 -->
        <mxCell id="monitoring-troubleshooting" value="&lt;b&gt;监控和故障排查指南:&lt;/b&gt;&lt;br&gt;&lt;br&gt;&lt;b&gt;1. 网络连通性检查:&lt;/b&gt;&lt;br&gt;# 检查OVS状态&lt;br&gt;ovs-vsctl show&lt;br&gt;ovs-ofctl dump-flows br0&lt;br&gt;&lt;br&gt;# 检查VLAN配置&lt;br&gt;ovs-vsctl list port&lt;br&gt;ovs-vsctl get port vnet0 tag&lt;br&gt;&lt;br&gt;&lt;b&gt;2. etcd集群状态:&lt;/b&gt;&lt;br&gt;etcdctl endpoint health&lt;br&gt;etcdctl member list&lt;br&gt;etcdctl endpoint status&lt;br&gt;&lt;br&gt;&lt;b&gt;3. 网络性能测试:&lt;/b&gt;&lt;br&gt;# 延迟测试&lt;br&gt;ping -c 10 **************&lt;br&gt;&lt;br&gt;# 带宽测试&lt;br&gt;iperf3 -s  # 在一个节点&lt;br&gt;iperf3 -c **************  # 在另一个节点&lt;br&gt;&lt;br&gt;&lt;b&gt;4. 常见问题排查:&lt;/b&gt;&lt;br&gt;• VLAN标签不匹配&lt;br&gt;• 防火墙规则阻塞&lt;br&gt;• etcd端口冲突&lt;br&gt;• 网络接口配置错误" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#f8cecc;strokeColor=#b85450;fontSize=9;align=left;" vertex="1" parent="1">
          <mxGeometry x="500" y="2200" width="400" height="280" as="geometry"/>
        </mxCell>

        <!-- 图例说明 -->
        <mxCell id="legend-explanation" value="&lt;b&gt;图例说明:&lt;/b&gt;&lt;br&gt;&lt;br&gt;&lt;table border=&quot;1&quot; style=&quot;border-collapse: collapse; width: 100%;&quot;&gt;&lt;tr&gt;&lt;td style=&quot;background-color: #dae8fc; padding: 5px;&quot;&gt;🔵 蓝色&lt;/td&gt;&lt;td style=&quot;padding: 5px;&quot;&gt;网络设备 (交换机)&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td style=&quot;background-color: #e1d5e7; padding: 5px;&quot;&gt;🟣 紫色&lt;/td&gt;&lt;td style=&quot;padding: 5px;&quot;&gt;宿主机和OVS网桥&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td style=&quot;background-color: #d5e8d4; padding: 5px;&quot;&gt;🟢 绿色&lt;/td&gt;&lt;td style=&quot;padding: 5px;&quot;&gt;etcd虚拟机节点&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td style=&quot;background-color: #fff2cc; padding: 5px;&quot;&gt;🟡 黄色&lt;/td&gt;&lt;td style=&quot;padding: 5px;&quot;&gt;配置说明和注释&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td style=&quot;padding: 5px;&quot;&gt;━━━ 实线&lt;/td&gt;&lt;td style=&quot;padding: 5px;&quot;&gt;物理网络连接&lt;/td&gt;&lt;/tr&gt;&lt;tr&gt;&lt;td style=&quot;padding: 5px;&quot;&gt;┅┅┅ 虚线&lt;/td&gt;&lt;td style=&quot;padding: 5px;&quot;&gt;etcd集群内部通信&lt;/td&gt;&lt;/tr&gt;&lt;/table&gt;&lt;br&gt;&lt;br&gt;&lt;b&gt;网络端口说明:&lt;/b&gt;&lt;br&gt;• 2379: etcd客户端通信端口&lt;br&gt;• 2380: etcd集群内部通信端口&lt;br&gt;• VLAN 100: 业务网络 (客户端访问)&lt;br&gt;• VLAN 200: 集群网络 (内部通信)&lt;br&gt;• VLAN 300: 管理网络 (SSH/监控)" style="shape=note;whiteSpace=wrap;html=1;backgroundOutline=1;darkOpacity=0.05;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=10;align=left;" vertex="1" parent="1">
          <mxGeometry x="950" y="2200" width="350" height="280" as="geometry"/>
        </mxCell>

      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
