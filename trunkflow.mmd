sequenceDiagram
    participant 虚拟机A(eth0.10)
    participant 云服务器密码机OVS网桥(ovs-br1)
    participant 物理交换机

    note over 虚拟机A(eth0.10): 子接口配置 IP=************ VLAN ID=10
    note over 云服务器密码机OVS网桥(ovs-br1): vnet0 配置为trunks=10,20

    虚拟机A(eth0.10)->>云服务器密码机OVS网桥(ovs-br1): 1、发送带 VLAN Tag=10 的数据包 (dst=**************)
    云服务器密码机OVS网桥(ovs-br1)->>物理交换机: 2、转发带 VLAN Tag 的数据包 (eth0)
    物理交换机->>物理交换机: 3、处理 VLAN 10 并路由
    物理交换机-->>云服务器密码机OVS网桥(ovs-br1): 4、返回响应包 (带 VLAN 10)
    云服务器密码机OVS网桥(ovs-br1)->>虚拟机A(eth0.10): 5、直接转发带 Tag 数据包
