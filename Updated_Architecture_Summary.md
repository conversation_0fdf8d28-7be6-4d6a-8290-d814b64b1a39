# 🔄 OVS VLAN 单宿主机etcd集群架构 - 更新说明

## 📋 主要修改内容

根据您的要求，我已经对 `OVS_VLAN_Single_Host_etcd_Cluster.drawio` 进行了以下重要修改：

### 🎯 核心变更

#### 1. **VIP负载均衡器部署位置调整**
- **原方案**: 独立的第4个VM节点部署HAProxy
- **新方案**: HAProxy部署在etcd-node-1节点上
- **VIP配置**: *************** 绑定到etcd-node-1的业务网络接口

#### 2. **网络配置简化**
- **原配置**: 3个网口 (业务、集群通信、管理)
- **新配置**: 2个网口 (业务、管理)
- **集群通信**: 使用业务网络 (VLAN 100) 进行etcd节点间通信

#### 3. **VLAN规划简化**
- **VLAN 100 (业务网络)**: 
  - etcd客户端访问 (端口2379)
  - etcd集群内部通信 (端口2380)
  - HAProxy负载均衡
- **VLAN 300 (管理网络)**:
  - SSH管理访问 (端口22)
  - 监控数据收集 (端口9100)

## 🏗️ 新架构详情

### Trunk模式配置

#### 物理架构
```
物理宿主机
├── etcd-node-1 + HAProxy (************** + VIP ***************)
├── etcd-node-2 (**************)
└── etcd-node-3 (**************)
```

#### 网络配置
```bash
# OVS配置 (简化版)
ovs-vsctl add-br br0
ovs-vsctl add-port br0 eth0
ovs-vsctl set port eth0 trunk=100,300

# VM网络接口 (以node-1为例)
ip link add link eth0 name eth0.100 type vlan id 100
ip link add link eth0 name eth0.300 type vlan id 300
ip addr add **************/24 dev eth0.100
ip addr add 192.168.300.11/24 dev eth0.300

# VIP配置 (仅在node-1上)
ip addr add ***************/24 dev eth0.100
```

#### HAProxy配置
```bash
# /etc/haproxy/haproxy.cfg (在etcd-node-1上)
frontend etcd_frontend
    bind ***************:2379
    default_backend etcd_backend

backend etcd_backend
    balance roundrobin
    option tcp-check
    server etcd1 127.0.0.1:2379 check      # 本机
    server etcd2 **************:2379 check # node-2
    server etcd3 **************:2379 check # node-3
```

### Access模式配置

#### OVS配置
```bash
# 创建双网桥
ovs-vsctl add-br br-business  # VLAN 100
ovs-vsctl add-br br-mgmt      # VLAN 300

# VM端口分配
ovs-vsctl add-port br-business vnet0-eth0  # node-1业务网络
ovs-vsctl add-port br-mgmt vnet0-eth1      # node-1管理网络
```

#### VM网络配置
```bash
# 直接配置物理接口 (以node-1为例)
ip addr add **************/24 dev eth0  # 业务网络
ip addr add 192.168.300.11/24 dev eth1  # 管理网络

# VIP配置 (仅在node-1上)
ip addr add ***************/24 dev eth0
```

## 🔧 etcd集群配置

### 简化的etcd配置
```bash
# etcd-node-1 配置
ETCD_NAME=etcd-node-1
ETCD_LISTEN_CLIENT_URLS=https://**************:2379
ETCD_LISTEN_PEER_URLS=https://**************:2380
ETCD_ADVERTISE_CLIENT_URLS=https://**************:2379
ETCD_INITIAL_ADVERTISE_PEER_URLS=https://**************:2380

# 集群初始化 (使用业务网络进行集群通信)
ETCD_INITIAL_CLUSTER=etcd-node-1=https://**************:2380,etcd-node-2=https://**************:2380,etcd-node-3=https://**************:2380
```

### 客户端访问
```bash
# 通过VIP访问集群 (推荐)
etcdctl --endpoints=https://***************:2379 get /test
etcdctl --endpoints=https://***************:2379 put /test "value"
```

## 📊 架构优势

### 1. **资源优化**
- ✅ 节省1个VM资源 (不需要独立的负载均衡器VM)
- ✅ 减少网络接口数量 (从3个减少到2个)
- ✅ 简化VLAN规划 (从3个减少到2个)

### 2. **配置简化**
- ✅ 减少OVS网桥数量 (Access模式从3个减少到2个)
- ✅ 简化VM网络配置
- ✅ 降低管理复杂度

### 3. **网络优化**
- ✅ 集群通信和客户端访问使用同一网络，减少网络跳转
- ✅ 管理网络独立，保证安全性
- ✅ 故障排查更简单

## ⚠️ 注意事项

### 1. **单点风险**
- etcd-node-1故障会同时影响etcd服务和VIP负载均衡
- 建议配置监控告警，及时发现故障

### 2. **资源竞争**
- HAProxy与etcd共享CPU和内存资源
- 建议适当增加node-1的资源配置

### 3. **故障转移**
- 可考虑配置简单的故障转移脚本
- 在node-1故障时，手动将VIP迁移到其他节点

## 🔍 监控建议

### 关键监控指标
```bash
# 检查HAProxy状态
systemctl status haproxy

# 检查VIP绑定
ip addr show eth0.100 | grep ***************

# 检查etcd集群健康
etcdctl --endpoints=https://***************:2379 endpoint health

# 检查后端节点状态
echo "show stat" | socat stdio /var/run/haproxy/admin.sock
```

## 📝 总结

这次修改实现了您要求的架构调整：
1. **VIP负载均衡器部署在etcd-node-1上**，节省资源
2. **集群内部通信使用业务网络**，简化网络配置
3. **节点只有2个网口**，降低配置复杂度

新架构在保持功能完整性的同时，显著简化了部署和管理复杂度，特别适合资源有限的小规模部署场景。
