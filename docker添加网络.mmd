sequenceDiagram
    participant WEB管理员
    participant 云服务器密码机虚机管理服务
    participant OVS服务
    participant 虚拟机

    WEB管理员->>云服务器密码机虚机管理服务: 1、启动 Docker 虚拟机
    云服务器密码机虚机管理服务->>OVS服务: 2、创建 veth pair（vethX <-> eth0）
    云服务器密码机虚机管理服务->>OVS服务: 3、将 vethX 接入 ovs-br1
    OVS服务->>vethX端口: 4、设置 VLAN tag=10 或 trunks=10,20
    OVS服务-->>云服务器密码机虚机管理服务: 5、端口绑定与 VLAN 配置确认
    云服务器密码机虚机管理服务->>虚拟机: 6、将 eth0 注入虚拟机网络命名空间
    云服务器密码机虚机管理服务->>虚拟机: 7、注入网络配置（IP/VLAN）
    虚拟机-->>OVS服务: 8、网络初始化完成
    OVS服务-->>云服务器密码机虚机管理服务: 9、虚拟机网络初始化完成
    云服务器密码机虚机管理服务-->>WEB管理员: 10、返回虚拟机网络初始化完成