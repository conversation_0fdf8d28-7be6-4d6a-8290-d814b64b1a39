#!/bin/bash

# 目标目录
TARGET_DIR="$HOME/.cache/vscode-cpptools"

# 截止日期：2025年1月1日
CUTOFF_DATE="2025-01-01"

# 转换为时间戳
CUTOFF_TIMESTAMP=$(date -d "$CUTOFF_DATE" +%s)

echo "🧹 正在清理 $TARGET_DIR 中 2025 年以前的文件..."

# 查找并删除修改时间早于 2025 年的文件和文件夹
find "$TARGET_DIR" -mindepth 1 -printf '%T@ %p\n' | while read -r line; do
    file_time=$(echo "$line" | cut -d' ' -f1)
    file_path=$(echo "$line" | cut -d' ' -f2-)
    file_time_int=${file_time%.*}
    if [ "$file_time_int" -lt "$CUTOFF_TIMESTAMP" ]; then
        echo "🗑️ 删除：$file_path"
        rm -rf "$file_path"
    fi
done

echo "✅ 清理完成"
