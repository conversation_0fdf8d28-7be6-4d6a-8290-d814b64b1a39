# Makefile for Protocol Identifier
# Author: Augment Agent
# Date: 2025-07-31

CC = gcc
CFLAGS = -Wall -Wextra -std=c99 -O2 -g
# 检测操作系统并设置相应的链接库
ifeq ($(OS),Windows_NT)
    LDFLAGS = -lws2_32
else
    LDFLAGS =
endif

# Source files
SOURCES = protocol_identifier.c
HEADERS = protocol_identifier.h
TEST_SOURCES = test_protocol_identifier.c
OBJECTS = $(SOURCES:.c=.o)
TEST_OBJECTS = $(TEST_SOURCES:.c=.o)

# Target names
LIBRARY = libprotocol_identifier.a
TEST_EXECUTABLE = test_protocol_identifier
EXAMPLE_EXECUTABLE = example_usage
PERF_EXECUTABLE = performance_comparison

# Default target
all: $(LIBRARY) $(TEST_EXECUTABLE) $(EXAMPLE_EXECUTABLE) $(PERF_EXECUTABLE)

# Build static library
$(LIBRARY): $(OBJECTS)
	ar rcs $@ $^
	@echo "Static library $(LIBRARY) created successfully"

# Build test executable
$(TEST_EXECUTABLE): $(TEST_OBJECTS) $(OBJECTS)
	$(CC) $(CFLAGS) -o $@ $^ $(LDFLAGS)
	@echo "Test executable $(TEST_EXECUTABLE) created successfully"

# Build example executable
$(EXAMPLE_EXECUTABLE): example_usage.o $(OBJECTS)
	$(CC) $(CFLAGS) -o $@ $^ $(LDFLAGS)
	@echo "Example executable $(EXAMPLE_EXECUTABLE) created successfully"

# Build performance test executable
$(PERF_EXECUTABLE): performance_comparison.o $(OBJECTS)
	$(CC) $(CFLAGS) -o $@ $^ $(LDFLAGS)
	@echo "Performance test executable $(PERF_EXECUTABLE) created successfully"

# Compile source files
%.o: %.c $(HEADERS)
	$(CC) $(CFLAGS) -c $< -o $@

# Run tests
test: $(TEST_EXECUTABLE)
	@echo "Running protocol identifier tests..."
	./$(TEST_EXECUTABLE)

# Run example
example: $(EXAMPLE_EXECUTABLE)
	@echo "Running usage example..."
	./$(EXAMPLE_EXECUTABLE)

# Run performance test
perf: $(PERF_EXECUTABLE)
	@echo "Running performance comparison..."
	./$(PERF_EXECUTABLE)

# Clean build artifacts
clean:
	rm -f *.o $(LIBRARY) $(TEST_EXECUTABLE) $(EXAMPLE_EXECUTABLE) $(PERF_EXECUTABLE)
	@echo "Clean completed"

# Install library (optional)
install: $(LIBRARY)
	@echo "Installing library to /usr/local/lib..."
	sudo cp $(LIBRARY) /usr/local/lib/
	sudo cp $(HEADERS) /usr/local/include/
	@echo "Installation completed"

# Uninstall library (optional)
uninstall:
	@echo "Uninstalling library..."
	sudo rm -f /usr/local/lib/$(LIBRARY)
	sudo rm -f /usr/local/include/$(HEADERS)
	@echo "Uninstallation completed"

# Check code style (requires cppcheck)
check:
	@echo "Running static analysis..."
	cppcheck --enable=all --std=c99 $(SOURCES) $(TEST_SOURCES)

# Generate documentation (requires doxygen)
docs:
	@echo "Generating documentation..."
	doxygen Doxyfile

# Debug build
debug: CFLAGS += -DDEBUG -g3
debug: clean all

# Release build
release: CFLAGS += -DNDEBUG -O3
release: clean all

# Show help
help:
	@echo "Available targets:"
	@echo "  all       - Build library and test executable (default)"
	@echo "  test      - Run test suite"
	@echo "  example   - Run usage example"
	@echo "  clean     - Remove build artifacts"
	@echo "  install   - Install library to system"
	@echo "  uninstall - Remove library from system"
	@echo "  check     - Run static analysis"
	@echo "  docs      - Generate documentation"
	@echo "  debug     - Build with debug flags"
	@echo "  release   - Build with optimization flags"
	@echo "  help      - Show this help message"

.PHONY: all test example clean install uninstall check docs debug release help
