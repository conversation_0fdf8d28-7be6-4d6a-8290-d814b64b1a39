# 网络架构文档

以下是物理连接和逻辑架构的可视化描述，包含文字示意图和关键注释。您可根据此文档使用工具（如 Draw.io、Visio）绘制详细拓扑图。

## 1. 物理连接图（真实设备层级）

```
[ 物理交换机 (Cisco/HPE) ]
       |
       | (以太网线, 端口eth1配置为Trunk)
       |
[ 服务器 (物理机) ]
├── 物理网卡: eth0 (连接交换机eth1)
├── Open vSwitch (软件交换机)
│   ├── 虚拟网桥: ovs-br0 (绑定eth0)
│   ├── 虚拟端口: vnet1 (VLAN 10)
│   └── 虚拟端口: vnet2 (VLAN 20)
└── 虚拟机
    ├── VM1 (网卡绑定vnet1, VLAN 10)
    └── VM2 (网卡绑定vnet2, VLAN 20)
```

### 关键点

- 物理交换机与服务器通过 **eth0 ↔ eth1** 直连，线缆需支持 VLAN Tagging（如 Cat6）
- OVS 作为虚拟交换机在服务器内部运行，通过 ovs-br0 整合物理和虚拟网络

## 2. 逻辑架构图（OVS与虚拟机关系）

```
+-----------------------------+
|       物理交换机            |
| (Trunk: VLAN 10,20,30)      |
+-------------+---------------+
              |
              | eth1 (Trunk)
+-------------+---------------+
|        服务器               |
| +-------------------------+ |
| |    Open vSwitch (OVS)    | |
| | +---------------------+  | |
| | |      ovs-br0        |  | |
| | |  (绑定eth0)         |  | |
| | +----------+----------+  | |
| |            |             | |
| |   +--------+---------+   | |
| |   | vnet1 (tag=10)   |   | |
| |   +------------------+   | |
| |   | vnet2 (tag=20)   |   | |
| |   +------------------+   | |
| +-------------------------+ |
| +-------------------------+ |
| |        虚拟机            | |
| |  +--------+  +--------+  | |
| |  | VM1    |  | VM2    |  | |
| |  | VLAN 10|  | VLAN 20|  | |
| |  +--------+  +--------+  | |
| +-------------------------+ |
+-----------------------------+
```

### 关键注释

- **红色箭头**：物理连接（eth0 ↔ eth1）
- **蓝色虚线**：OVS 内部虚拟端口与虚拟机的绑定关系
- **VLAN 标签**：数据包从 VM 到 OVS 时会被打上对应 VLAN Tag（如 vnet1 → tag=10）

## 3. 数据流示例（VM1 → 外部网络）

```
VM1 (VLAN 10)
 → vnet1 (OVS添加VLAN 10 Tag)
 → ovs-br0
 → eth0 (Trunk透传Tag)
 → 物理交换机eth1
 → 交换机根据VLAN 10路由到目标
```