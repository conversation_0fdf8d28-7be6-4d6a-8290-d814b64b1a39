<mxfile host="65bd71144e" pages="2">
    <diagram name="ACCESS模式" id="access-mode-topology">
        <mxGraphModel dx="486" dy="440" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="title-access" value="OVS ACCESS模式网络拓扑" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="400" y="30" width="300" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="physical-switch" value="物理交换机" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=14;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="480" y="100" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="host-a" value="宿主机A" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=14;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="200" y="220" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="host-b" value="宿主机B" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=14;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="760" y="220" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="host-a-nic" value="eth1" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
                    <mxGeometry x="240" y="300" width="40" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="host-b-nic" value="eth1" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
                    <mxGeometry x="800" y="300" width="40" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="ovs-br-a" value="OVS网桥 br0" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=12;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="180" y="360" width="160" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="ovs-br-b" value="OVS网桥 br0" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=12;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="740" y="360" width="160" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="vm-a1" value="VM-A1&#xa;VLAN 10" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11;" parent="1" vertex="1">
                    <mxGeometry x="120" y="460" width="80" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="vm-a2" value="VM-A2&#xa;VLAN 20" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11;" parent="1" vertex="1">
                    <mxGeometry x="220" y="460" width="80" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="vm-a3" value="VM-A3&#xa;VLAN 10" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11;" parent="1" vertex="1">
                    <mxGeometry x="320" y="460" width="80" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="vm-b1" value="VM-B1&#xa;VLAN 10" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11;" parent="1" vertex="1">
                    <mxGeometry x="680" y="460" width="80" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="vm-b2" value="VM-B2&#xa;VLAN 20" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11;" parent="1" vertex="1">
                    <mxGeometry x="780" y="460" width="80" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="vm-b3" value="VM-B3&#xa;VLAN 10" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11;" parent="1" vertex="1">
                    <mxGeometry x="880" y="460" width="80" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="conn1" value="" style="endArrow=classic;html=1;rounded=0;" parent="1" source="physical-switch" target="host-a" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="400" y="300" as="sourcePoint"/>
                        <mxPoint x="450" y="250" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="conn2" value="" style="endArrow=classic;html=1;rounded=0;" parent="1" source="physical-switch" target="host-b" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="400" y="300" as="sourcePoint"/>
                        <mxPoint x="450" y="250" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="conn3" value="" style="endArrow=classic;html=1;rounded=0;" parent="1" source="host-a" target="host-a-nic" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="400" y="300" as="sourcePoint"/>
                        <mxPoint x="450" y="250" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="conn4" value="" style="endArrow=classic;html=1;rounded=0;" parent="1" source="host-b" target="host-b-nic" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="400" y="300" as="sourcePoint"/>
                        <mxPoint x="450" y="250" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="conn5" value="" style="endArrow=classic;html=1;rounded=0;" parent="1" source="host-a-nic" target="ovs-br-a" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="400" y="300" as="sourcePoint"/>
                        <mxPoint x="450" y="250" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="conn6" value="" style="endArrow=classic;html=1;rounded=0;" parent="1" source="host-b-nic" target="ovs-br-b" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="400" y="300" as="sourcePoint"/>
                        <mxPoint x="450" y="250" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="conn7" value="" style="endArrow=classic;html=1;rounded=0;" parent="1" source="ovs-br-a" target="vm-a1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="400" y="300" as="sourcePoint"/>
                        <mxPoint x="450" y="250" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="conn8" value="" style="endArrow=classic;html=1;rounded=0;" parent="1" source="ovs-br-a" target="vm-a2" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="400" y="300" as="sourcePoint"/>
                        <mxPoint x="450" y="250" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="conn9" value="" style="endArrow=classic;html=1;rounded=0;" parent="1" source="ovs-br-a" target="vm-a3" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="400" y="300" as="sourcePoint"/>
                        <mxPoint x="450" y="250" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="conn10" value="" style="endArrow=classic;html=1;rounded=0;" parent="1" source="ovs-br-b" target="vm-b1" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="400" y="300" as="sourcePoint"/>
                        <mxPoint x="450" y="250" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="conn11" value="" style="endArrow=classic;html=1;rounded=0;" parent="1" source="ovs-br-b" target="vm-b2" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="400" y="300" as="sourcePoint"/>
                        <mxPoint x="450" y="250" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="conn12" value="" style="endArrow=classic;html=1;rounded=0;" parent="1" source="ovs-br-b" target="vm-b3" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="400" y="300" as="sourcePoint"/>
                        <mxPoint x="450" y="250" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="note-access" value="ACCESS模式说明：&#xa;• 每个虚拟机端口只属于一个VLAN&#xa;• 虚拟机发送的数据包不带VLAN标签&#xa;• OVS网桥为数据包添加VLAN标签&#xa;• 同VLAN虚拟机可以互相通信" style="text;html=1;strokeColor=#82b366;fillColor=#d5e8d4;align=left;verticalAlign=top;whiteSpace=wrap;rounded=1;fontSize=11;" parent="1" vertex="1">
                    <mxGeometry x="450" y="450" width="200" height="100" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
    <diagram name="TRUNK模式" id="trunk-mode-topology">
        <mxGraphModel dx="486" dy="440" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1169" pageHeight="827" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="title-trunk" value="OVS TRUNK模式网络拓扑" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="400" y="30" width="300" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="physical-switch-trunk" value="物理交换机" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontSize=14;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="480" y="100" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="host-a-trunk" value="宿主机A" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=14;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="200" y="220" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="host-b-trunk" value="宿主机B" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;fontSize=14;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="760" y="220" width="120" height="60" as="geometry"/>
                </mxCell>
                <mxCell id="host-a-nic-trunk" value="eth1" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
                    <mxGeometry x="240" y="300" width="40" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="host-b-nic-trunk" value="eth1" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
                    <mxGeometry x="800" y="300" width="40" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="ovs-br-a-trunk" value="OVS网桥 br0" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=12;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="180" y="360" width="160" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="ovs-br-b-trunk" value="OVS网桥 br0" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;fontSize=12;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="740" y="360" width="160" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="vm-a1-trunk" value="VM-A1&#xa;多VLAN支持" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11;" parent="1" vertex="1">
                    <mxGeometry x="120" y="460" width="80" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="vm-a2-trunk" value="VM-A2&#xa;多VLAN支持" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11;" parent="1" vertex="1">
                    <mxGeometry x="320" y="460" width="80" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="vm-b1-trunk" value="VM-B1&#xa;多VLAN支持" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11;" parent="1" vertex="1">
                    <mxGeometry x="680" y="460" width="80" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="vm-b2-trunk" value="VM-B2&#xa;多VLAN支持" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;fontSize=11;" parent="1" vertex="1">
                    <mxGeometry x="880" y="460" width="80" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="trunk-port-a" value="TRUNK" style="text;html=1;strokeColor=#d79b00;fillColor=#ffe6cc;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=10;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="220" y="420" width="50" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="trunk-port-b" value="TRUNK" style="text;html=1;strokeColor=#d79b00;fillColor=#ffe6cc;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=1;fontSize=10;fontStyle=1;" parent="1" vertex="1">
                    <mxGeometry x="780" y="420" width="50" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="conn1-trunk" value="" style="endArrow=classic;html=1;rounded=0;" parent="1" source="physical-switch-trunk" target="host-a-trunk" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="400" y="300" as="sourcePoint"/>
                        <mxPoint x="450" y="250" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="conn2-trunk" value="" style="endArrow=classic;html=1;rounded=0;" parent="1" source="physical-switch-trunk" target="host-b-trunk" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="400" y="300" as="sourcePoint"/>
                        <mxPoint x="450" y="250" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="conn3-trunk" value="" style="endArrow=classic;html=1;rounded=0;" parent="1" source="host-a-trunk" target="host-a-nic-trunk" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="400" y="300" as="sourcePoint"/>
                        <mxPoint x="450" y="250" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="conn4-trunk" value="" style="endArrow=classic;html=1;rounded=0;" parent="1" source="host-b-trunk" target="host-b-nic-trunk" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="400" y="300" as="sourcePoint"/>
                        <mxPoint x="450" y="250" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="conn5-trunk" value="" style="endArrow=classic;html=1;rounded=0;" parent="1" source="host-a-nic-trunk" target="ovs-br-a-trunk" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="400" y="300" as="sourcePoint"/>
                        <mxPoint x="450" y="250" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="conn6-trunk" value="" style="endArrow=classic;html=1;rounded=0;" parent="1" source="host-b-nic-trunk" target="ovs-br-b-trunk" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="400" y="300" as="sourcePoint"/>
                        <mxPoint x="450" y="250" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="conn7-trunk" value="" style="endArrow=classic;html=1;rounded=0;" parent="1" source="ovs-br-a-trunk" target="vm-a1-trunk" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="400" y="300" as="sourcePoint"/>
                        <mxPoint x="450" y="250" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="conn8-trunk" value="" style="endArrow=classic;html=1;rounded=0;" parent="1" source="ovs-br-a-trunk" target="vm-a2-trunk" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="400" y="300" as="sourcePoint"/>
                        <mxPoint x="450" y="250" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="conn9-trunk" value="" style="endArrow=classic;html=1;rounded=0;" parent="1" source="ovs-br-b-trunk" target="vm-b1-trunk" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="400" y="300" as="sourcePoint"/>
                        <mxPoint x="450" y="250" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="conn10-trunk" value="" style="endArrow=classic;html=1;rounded=0;" parent="1" source="ovs-br-b-trunk" target="vm-b2-trunk" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="400" y="300" as="sourcePoint"/>
                        <mxPoint x="450" y="250" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="note-trunk" value="TRUNK模式说明：&#xa;• 虚拟机端口可以承载多个VLAN&#xa;• 虚拟机发送的数据包带VLAN标签&#xa;• OVS网桥透传VLAN标签&#xa;• 支持虚拟机内部VLAN配置&#xa;• 适用于需要多VLAN支持的场景" style="text;html=1;strokeColor=#d79b00;fillColor=#ffe6cc;align=left;verticalAlign=top;whiteSpace=wrap;rounded=1;fontSize=11;" parent="1" vertex="1">
                    <mxGeometry x="450" y="450" width="200" height="120" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>