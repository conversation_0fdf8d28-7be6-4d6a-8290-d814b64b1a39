/**
 * @file protocol_identifier.c
 * @brief TCP协议识别接口实现
 * <AUTHOR> Agent
 * @date 2025-07-31
 */

#include "protocol_identifier.h"
#include <string.h>

/* 跨平台字节序转换 */
#ifdef _WIN32
    #include <winsock2.h>
    #pragma comment(lib, "ws2_32.lib")
#else
    #include <arpa/inet.h>
#endif

/* 默认配置参数 */
static const PROTOCOL_CONFIG default_config = {
    .max_packet_size = 1024 * 1024,  /* 1MB最大包大小 */
    .min_action = 1,                 /* 最小action值 */
    .max_action = 9999,              /* 最大action值 */
    .strict_asn1_check = 1           /* 启用严格ASN.1检查 */
};

/**
 * @brief 检查ASN.1标签是否有效
 */
static int is_valid_asn1_tag(unsigned char tag) {
    /* 检查常见的ASN.1标签 */
    switch (tag) {
        case ASN1_SEQUENCE:
        case ASN1_SET:
        case ASN1_INTEGER:
        case ASN1_OCTET_STRING:
        case ASN1_NULL:
        case ASN1_OID:
        case ASN1_UTF8_STRING:
        case ASN1_PRINTABLE_STR:
        case ASN1_T61_STRING:
        case ASN1_IA5_STRING:
        case ASN1_UTC_TIME:
        case ASN1_GENERALIZED_TIME:
            return 1;
        default:
            /* 检查是否为构造类型或上下文特定类型 */
            if ((tag & 0x20) || (tag & 0x80)) {
                return 1;  /* 构造类型或上下文特定类型 */
            }
            /* 检查是否为应用类型 */
            if ((tag & 0x40)) {
                return 1;  /* 应用类型 */
            }
            return 0;
    }
}

/**
 * @brief 验证ASN.1长度编码
 */
int validate_asn1_length(const unsigned char* data, size_t data_len, 
                        size_t* content_len) {
    if (data_len < 2) return 0;
    
    unsigned char length_byte = data[1];
    
    /* 短格式长度 (0-127) */
    if ((length_byte & 0x80) == 0) {
        *content_len = length_byte;
        /* 检查数据长度是否足够包含标签+长度+内容 */
        return (data_len >= 2 + *content_len) ? 1 : 0;
    }
    
    /* 长格式长度 */
    int length_octets = length_byte & 0x7F;
    if (length_octets == 0 || length_octets > 4) {
        return 0;  /* 不定长度或长度过长 */
    }
    
    if (data_len < 2 + length_octets) {
        return 0;  /* 数据不足 */
    }
    
    /* 计算实际长度 */
    size_t actual_length = 0;
    for (int i = 0; i < length_octets; i++) {
        actual_length = (actual_length << 8) | data[2 + i];
    }
    
    *content_len = actual_length;
    return (data_len >= 2 + length_octets + actual_length) ? 1 : 0;
}

/**
 * @brief 检查是否为ASN.1 DER编码
 */
int is_asn1_der_format(const unsigned char* data, size_t data_len) {
    if (!data || data_len < MIN_ASN1_PACKET_SIZE) {
        return 0;
    }

    /* 检查第一个字节是否为有效的ASN.1标签 */
    if (!is_valid_asn1_tag(data[0])) {
        return 0;
    }

    /* 验证长度编码 */
    size_t content_len;
    if (!validate_asn1_length(data, data_len, &content_len)) {
        return 0;
    }
    
    /* 对于SEQUENCE和SET，进一步验证内容 */
    if (data[0] == ASN1_SEQUENCE || data[0] == ASN1_SET) {
        /* 计算头部长度 */
        size_t header_len = 2;
        if (data[1] & 0x80) {
            header_len += (data[1] & 0x7F);
        }

        /* 检查内容是否也符合ASN.1格式 */
        if (content_len > 0 && data_len >= header_len) {
            /* 如果有内容，检查第一个元素是否为有效ASN.1标签 */
            if (data_len > header_len) {
                return is_valid_asn1_tag(data[header_len]);
            }
            /* 空的SEQUENCE/SET也是有效的 */
            return 1;
        }
    }
    
    return 1;
}

/**
 * @brief 计算简单校验和
 */
uint32_t calculate_simple_checksum(const unsigned char* data, size_t len) {
    uint32_t checksum = 0;
    for (size_t i = 0; i < len; i++) {
        checksum += data[i];
    }
    return checksum;
}

/**
 * @brief 检查是否为自定义HSM协议
 */
int is_custom_hsm_format(const unsigned char* data, size_t data_len) {
    if (!data || data_len < MIN_CUSTOM_PACKET_SIZE) {
        return 0;
    }
    
    /* 直接使用指针转换（1字节对齐保证安全） */
    const HSM_REQ_HEAD *header = (const HSM_REQ_HEAD *)data;

    /* 转换字节序（假设网络字节序） */
    uint32_t tlen = ntohl(header->tlen);
    uint32_t checksum = ntohl(header->checksum);
    uint16_t action = ntohs(header->action);
    uint16_t version = ntohs(header->version);
    
    /* 验证tlen字段合理性 */
    if (tlen < MIN_CUSTOM_PACKET_SIZE ||
        tlen > default_config.max_packet_size) {
        return 0;
    }

    /* 验证tlen与实际数据长度的一致性 */
    if (tlen != data_len) {
        /* 允许部分数据包的情况 */
        if (tlen < data_len) {
            return 0;  /* tlen不应该小于当前数据长度 */
        }
    }

    /* 验证action字段合理性 */
    if (action < default_config.min_action ||
        action > default_config.max_action) {
        return 0;
    }

    /* 验证版本号合理性（主版本号不应该过大） */
    uint8_t major_version = (version >> 8) & 0xFF;
    if (major_version > 10) {  /* 假设主版本号不超过10 */
        return 0;
    }

    /* 如果有足够的数据，验证校验和 */
    if (data_len >= tlen && tlen > sizeof(HSM_REQ_HEAD)) {
        uint32_t calculated_checksum = calculate_simple_checksum(
            data + sizeof(HSM_REQ_HEAD),
            tlen - sizeof(HSM_REQ_HEAD)
        );

        if (calculated_checksum != checksum) {
            return 0;  /* 校验和不匹配 */
        }
    }
    
    return 1;
}

/**
 * @brief 协议识别主函数
 */
int identify_protocol(const unsigned char* data, size_t data_len) {
    return identify_protocol_ex(data, data_len, &default_config);
}

/**
 * @brief 带配置参数的协议识别函数
 */
int identify_protocol_ex(const unsigned char* data, size_t data_len, 
                        const PROTOCOL_CONFIG* config) {
    if (!data || data_len == 0) {
        return PROTOCOL_UNKNOWN;
    }
    
    const PROTOCOL_CONFIG* cfg = config ? config : &default_config;
    
    /* 检查数据包大小限制 */
    if (data_len > cfg->max_packet_size) {
        return PROTOCOL_UNKNOWN;
    }
    
    /* 优先检查自定义协议（通常更快） */
    if (data_len >= MIN_CUSTOM_PACKET_SIZE) {
        if (is_custom_hsm_format(data, data_len)) {
            return PROTOCOL_CUSTOM;
        }
    }
    
    /* 检查ASN.1协议 */
    if (data_len >= MIN_ASN1_PACKET_SIZE) {
        if (is_asn1_der_format(data, data_len)) {
            return PROTOCOL_ASN1;
        }
    }
    
    return PROTOCOL_UNKNOWN;
}

/**
 * @brief 获取协议类型字符串描述
 */
const char* get_protocol_name(int protocol_type) {
    switch (protocol_type) {
        case PROTOCOL_ASN1:
            return "ASN.1 DER";
        case PROTOCOL_CUSTOM:
            return "Custom HSM";
        case PROTOCOL_UNKNOWN:
        default:
            return "Unknown";
    }
}
