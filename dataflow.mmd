sequenceDiagram
    participant 虚拟机A
    participant 云服务器密码机OVS网桥(ovs-br1)
    participant 物理交换机

    note over 云服务器密码机OVS网桥(ovs-br1): vnet0/veth0 端口已配置 tag=10

    虚拟机A->>云服务器密码机OVS网桥(ovs-br1): 1、发送数据包(不带 VLAN Tag)
    云服务器密码机OVS网桥(ovs-br1)->>云服务器密码机OVS网桥(ovs-br1): 2、添加 VLAN Tag=10
    云服务器密码机OVS网桥(ovs-br1)->>物理交换机: 3、转发带 Tag=10 的数据包 (通过物理网卡eth1)
    物理交换机->>物理交换机: 4、VLAN 10 区域内部路由或转发
    物理交换机-->>云服务器密码机OVS网桥(ovs-br1): 5、响应包返回 (带 VLAN 10)
    云服务器密码机OVS网桥(ovs-br1)->>云服务器密码机OVS网桥(ovs-br1): 6、剥离 VLAN Tag
    云服务器密码机OVS网桥(ovs-br1)-->>虚拟机A: 7、原始数据包送达
