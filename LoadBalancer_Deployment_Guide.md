# 🏗️ VIP负载均衡器部署方案详解

## 📋 概述

在单宿主机etcd集群架构中，VIP负载均衡器的部署位置是一个关键的架构决策。本文档详细分析了三种主要的部署方案及其优缺点。

## 🎯 部署方案对比

### 方案1: 独立负载均衡器节点 (推荐)

#### 架构图：
```
物理宿主机 (*************/24)
├── etcd-node-1    (**************)
├── etcd-node-2    (**************) 
├── etcd-node-3    (*************3)
└── lb-node        (***************) ← VIP: ***************
```

#### 配置详情：
```bash
# 负载均衡器VM配置
CPU: 2核
内存: 4GB
网络: eth0 (***************/24)
VIP: *************** (绑定到eth0)
服务: HAProxy/Nginx
```

#### 优势：
- ✅ **高可用性**: 负载均衡器独立运行，不受etcd节点故障影响
- ✅ **性能隔离**: 有独立的CPU和内存资源，不与etcd竞争
- ✅ **维护方便**: 可以独立升级、重启负载均衡器
- ✅ **扩展性好**: 易于实现负载均衡器高可用 (主备模式)
- ✅ **故障隔离**: etcd节点故障不会影响负载均衡器

#### 劣势：
- ❌ **资源开销**: 需要额外的VM资源
- ❌ **管理复杂**: 多了一个组件需要管理

### 方案2: 与etcd节点共存

#### 架构图：
```
物理宿主机 (*************/24)
├── etcd-node-1 + HAProxy (**************) ← VIP: ***************
├── etcd-node-2           (**************)
└── etcd-node-3           (*************3)
```

#### 配置详情：
```bash
# etcd-node-1 配置
CPU: 4核 (etcd + HAProxy共享)
内存: 8GB (etcd + HAProxy共享)
网络: eth0 (**************/24)
VIP: *************** (绑定到eth0)
服务: etcd + HAProxy
```

#### 优势：
- ✅ **资源节省**: 不需要额外的VM
- ✅ **配置简单**: 减少了一个组件的管理
- ✅ **成本低**: 适合资源有限的环境

#### 劣势：
- ❌ **单点故障**: 如果etcd-node-1故障，VIP也会失效
- ❌ **资源竞争**: 负载均衡器与etcd共享CPU和内存
- ❌ **维护困难**: 重启etcd会影响负载均衡器
- ❌ **性能影响**: 负载均衡器处理会影响etcd性能

### 方案3: 双负载均衡器高可用 (生产推荐)

#### 架构图：
```
物理宿主机 (*************/24)
├── etcd-node-1    (**************)
├── etcd-node-2    (**************)
├── etcd-node-3    (*************3)
├── lb-node-1      (***************) ← 主负载均衡器
└── lb-node-2      (***************) ← 备负载均衡器
                   VIP: *************** (VRRP漂移)
```

#### 配置详情：
```bash
# 主负载均衡器 (lb-node-1)
CPU: 2核
内存: 4GB
网络: eth0 (***************/24)
服务: HAProxy + keepalived (MASTER)

# 备负载均衡器 (lb-node-2)  
CPU: 2核
内存: 4GB
网络: eth0 (***************/24)
服务: HAProxy + keepalived (BACKUP)

# VIP配置
VIP: ***************
VRRP Virtual Router ID: 52
```

#### 优势：
- ✅ **最高可用性**: 负载均衡器本身也有高可用保障
- ✅ **无单点故障**: 任何单个组件故障都不会影响服务
- ✅ **生产级别**: 适合对可用性要求极高的环境
- ✅ **故障转移快**: 负载均衡器故障转移 < 3秒

#### 劣势：
- ❌ **资源开销大**: 需要2个额外的VM
- ❌ **配置复杂**: 需要配置VRRP和健康检查
- ❌ **成本高**: 适合大型生产环境

## 🛠️ 具体实现配置

### 方案1: 独立负载均衡器配置

#### 1. 创建负载均衡器VM
```bash
# 在宿主机上创建第4个VM
virt-install \
  --name lb-node \
  --ram 4096 \
  --vcpus 2 \
  --disk path=/var/lib/libvirt/images/lb-node.qcow2,size=20 \
  --network bridge=br0 \
  --os-type linux \
  --os-variant ubuntu20.04 \
  --cdrom /path/to/ubuntu-20.04.iso
```

#### 2. 配置网络接口
```bash
# 在lb-node上配置网络
sudo ip addr add ***************/24 dev eth0
sudo ip link set eth0 up
sudo ip route add default via *************

# 配置VIP
sudo ip addr add ***************/24 dev eth0
```

#### 3. 安装和配置HAProxy
```bash
# 安装HAProxy
sudo apt-get update
sudo apt-get install haproxy

# 配置文件 /etc/haproxy/haproxy.cfg
global
    daemon
    log stdout local0

defaults
    mode tcp
    timeout connect 5000ms
    timeout client 50000ms
    timeout server 50000ms

frontend etcd_frontend
    bind ***************:2379
    default_backend etcd_backend

backend etcd_backend
    balance roundrobin
    option tcp-check
    server etcd1 **************:2379 check inter 2000 rise 2 fall 3
    server etcd2 **************:2379 check inter 2000 rise 2 fall 3
    server etcd3 *************3:2379 check inter 2000 rise 2 fall 3

# 启动服务
sudo systemctl enable haproxy
sudo systemctl start haproxy
```

### 方案2: 共存部署配置

#### 在etcd-node-1上安装HAProxy
```bash
# 在etcd-node-1 (**************) 上执行
sudo apt-get install haproxy

# 配置VIP
sudo ip addr add ***************/24 dev eth0

# HAProxy配置 (同上)
# 注意: 后端服务器包含本机
backend etcd_backend
    balance roundrobin
    option tcp-check
    server etcd1 127.0.0.1:2379 check inter 2000 rise 2 fall 3
    server etcd2 **************:2379 check inter 2000 rise 2 fall 3
    server etcd3 *************3:2379 check inter 2000 rise 2 fall 3
```

### 方案3: 双负载均衡器配置

#### 主负载均衡器 (lb-node-1) keepalived配置
```bash
# /etc/keepalived/keepalived.conf
vrrp_script chk_haproxy {
    script "/usr/bin/killall -0 haproxy"
    interval 2
    weight -2
    fall 3
    rise 2
}

vrrp_instance VI_1 {
    state MASTER
    interface eth0
    virtual_router_id 52
    priority 150
    advert_int 1
    authentication {
        auth_type PASS
        auth_pass lb_password
    }
    virtual_ipaddress {
        ***************
    }
    track_script {
        chk_haproxy
    }
}
```

#### 备负载均衡器 (lb-node-2) keepalived配置
```bash
# /etc/keepalived/keepalived.conf
vrrp_script chk_haproxy {
    script "/usr/bin/killall -0 haproxy"
    interval 2
    weight -2
    fall 3
    rise 2
}

vrrp_instance VI_1 {
    state BACKUP
    interface eth0
    virtual_router_id 52
    priority 100
    advert_int 1
    authentication {
        auth_type PASS
        auth_pass lb_password
    }
    virtual_ipaddress {
        ***************
    }
    track_script {
        chk_haproxy
    }
}
```

## 📊 方案选择建议

| 环境类型 | 推荐方案 | 理由 |
|---------|---------|------|
| **开发测试** | 方案2 (共存) | 资源节省，配置简单 |
| **小型生产** | 方案1 (独立) | 平衡可用性和成本 |
| **大型生产** | 方案3 (双LB) | 最高可用性保障 |
| **资源受限** | 方案2 (共存) | 最小资源消耗 |

## 🔍 监控和故障排查

### 检查负载均衡器状态
```bash
# 检查HAProxy状态
sudo systemctl status haproxy

# 查看HAProxy统计信息
echo "show stat" | socat stdio /var/run/haproxy/admin.sock

# 检查VIP绑定
ip addr show eth0 | grep ***************

# 测试VIP连通性
curl -k https://***************:2379/health
```

### 故障转移测试
```bash
# 测试负载均衡器故障转移 (方案3)
sudo systemctl stop haproxy  # 在主负载均衡器上
# 观察VIP是否漂移到备负载均衡器

# 测试etcd节点故障转移
sudo systemctl stop etcd  # 在某个etcd节点上
# 观察HAProxy是否自动剔除故障节点
```

## 📝 总结

对于单宿主机etcd集群，推荐使用**方案1 (独立负载均衡器)**作为默认选择，它在可用性、性能和复杂度之间取得了良好的平衡。对于生产环境，可以考虑**方案3 (双负载均衡器)**以获得最高的可用性保障。
