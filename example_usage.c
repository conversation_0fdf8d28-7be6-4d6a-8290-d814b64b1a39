/**
 * @file example_usage.c
 * @brief 协议识别接口使用示例
 * <AUTHOR> Agent
 * @date 2025-07-31
 */

#include "protocol_identifier.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

/* 跨平台网络头文件 */
#ifdef _WIN32
    #include <winsock2.h>
    #include <ws2tcpip.h>
    #pragma comment(lib, "ws2_32.lib")
#else
    #include <unistd.h>
    #include <sys/socket.h>
    #include <netinet/in.h>
    #include <arpa/inet.h>
#endif

/**
 * @brief 模拟TCP服务器处理函数
 */
void handle_client_data(const unsigned char* data, size_t data_len, 
                       const char* client_info) {
    printf("\n--- Processing data from %s ---\n", client_info);
    printf("Data length: %zu bytes\n", data_len);
    
    /* 识别协议类型 */
    int protocol_type = identify_protocol(data, data_len);
    printf("Protocol identified: %s (%d)\n", 
           get_protocol_name(protocol_type), protocol_type);
    
    /* 根据协议类型进行不同的处理 */
    switch (protocol_type) {
        case PROTOCOL_ASN1:
            printf("Processing ASN.1 DER encoded data...\n");
            /* 这里可以调用ASN.1解析函数 */
            printf("  - Parsing ASN.1 structure\n");
            printf("  - Validating certificates/signatures\n");
            printf("  - Extracting key information\n");
            break;
            
        case PROTOCOL_CUSTOM:
            printf("Processing custom HSM protocol data...\n");
            /* 这里可以调用HSM协议处理函数 */
            if (data_len >= sizeof(HSM_REQ_HEAD)) {
                HSM_REQ_HEAD header;
                memcpy(&header, data, sizeof(HSM_REQ_HEAD));
                
                /* 转换字节序 */
                header.tlen = ntohl(header.tlen);
                header.action = ntohs(header.action);
                header.version = ntohs(header.version);
                
                printf("  - Total length: %u bytes\n", header.tlen);
                printf("  - Action code: %u\n", header.action);
                printf("  - Version: %u.%u\n", 
                       (header.version >> 8) & 0xFF, 
                       header.version & 0xFF);
                printf("  - Executing HSM operation\n");
            }
            break;
            
        case PROTOCOL_UNKNOWN:
        default:
            printf("Unknown protocol - rejecting connection\n");
            printf("  - Logging security event\n");
            printf("  - Closing connection\n");
            break;
    }
    
    printf("--- Processing completed ---\n");
}

/**
 * @brief 创建示例ASN.1数据包
 */
void create_sample_asn1_data(unsigned char* buffer, size_t* len) {
    /* 模拟一个简单的证书请求结构 */
    unsigned char asn1_cert_req[] = {
        0x30, 0x82, 0x01, 0x20,        /* SEQUENCE, length 288 (0x120) */
        0x30, 0x81, 0x80,              /* SEQUENCE, length 128 */
        0x02, 0x01, 0x00,              /* INTEGER 0 (version) */
        0x30, 0x0D,                    /* SEQUENCE, length 13 */
        0x06, 0x09, 0x2A, 0x86, 0x48, 0x86, 0xF7, 0x0D, 0x01, 0x01, 0x01, /* RSA OID */
        0x05, 0x00,                    /* NULL */
        0x04, 0x20,                    /* OCTET STRING, length 32 */
        /* 32 bytes of dummy key data */
        0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
        0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F, 0x10,
        0x11, 0x12, 0x13, 0x14, 0x15, 0x16, 0x17, 0x18,
        0x19, 0x1A, 0x1B, 0x1C, 0x1D, 0x1E, 0x1F, 0x20
    };
    
    memcpy(buffer, asn1_cert_req, sizeof(asn1_cert_req));
    *len = sizeof(asn1_cert_req);
}

/**
 * @brief 创建示例HSM数据包
 */
void create_sample_hsm_data(unsigned char* buffer, size_t* len) {
    HSM_REQ_HEAD header;
    const char* command_data = "GENERATE_KEY_PAIR RSA 2048";
    size_t command_len = strlen(command_data);
    
    /* 填充HSM包头 */
    header.tlen = htonl(sizeof(HSM_REQ_HEAD) + command_len);
    header.checksum = htonl(calculate_simple_checksum(
        (unsigned char*)command_data, command_len));
    header.action = htons(5001);  /* 密钥生成操作 */
    header.version = htons(0x0201);  /* 版本2.1 */
    header.reserve = htonl(0x12345678);  /* 会话ID */
    
    /* 组装完整数据包 */
    memcpy(buffer, &header, sizeof(HSM_REQ_HEAD));
    memcpy(buffer + sizeof(HSM_REQ_HEAD), command_data, command_len);
    *len = sizeof(HSM_REQ_HEAD) + command_len;
}

/**
 * @brief 模拟网络数据接收和处理
 */
void simulate_network_processing() {
    printf("=== Simulating Network Data Processing ===\n");
    
    unsigned char buffer[1024];
    size_t data_len;
    
    /* 模拟接收ASN.1数据包 */
    printf("\n1. Simulating ASN.1 certificate request...\n");
    create_sample_asn1_data(buffer, &data_len);
    handle_client_data(buffer, data_len, "Client 192.168.1.100:8443");
    
    /* 模拟接收HSM数据包 */
    printf("\n2. Simulating HSM key generation request...\n");
    create_sample_hsm_data(buffer, &data_len);
    handle_client_data(buffer, data_len, "HSM Client 10.0.0.50:9000");
    
    /* 模拟接收未知数据包 */
    printf("\n3. Simulating unknown protocol data...\n");
    unsigned char unknown_data[] = {
        0xFF, 0xFE, 0xFD, 0xFC, 0xFB, 0xFA, 0xF9, 0xF8,
        0xF7, 0xF6, 0xF5, 0xF4, 0xF3, 0xF2, 0xF1, 0xF0,
        0xEF, 0xEE, 0xED, 0xEC, 0xEB, 0xEA, 0xE9, 0xE8
    };
    handle_client_data(unknown_data, sizeof(unknown_data), 
                      "Suspicious Client 192.168.1.200:12345");
}

/**
 * @brief 演示配置参数的使用
 */
void demonstrate_configuration() {
    printf("\n=== Demonstrating Configuration Options ===\n");
    
    /* 创建自定义配置 */
    PROTOCOL_CONFIG custom_config = {
        .max_packet_size = 64 * 1024,    /* 64KB最大包大小 */
        .min_action = 1000,              /* HSM action范围: 1000-8999 */
        .max_action = 8999,
        .strict_asn1_check = 0           /* 宽松的ASN.1检查 */
    };
    
    unsigned char buffer[1024];
    size_t data_len;
    
    /* 测试使用自定义配置 */
    create_sample_hsm_data(buffer, &data_len);
    
    printf("Using default configuration:\n");
    int result1 = identify_protocol(buffer, data_len);
    printf("  Result: %s\n", get_protocol_name(result1));
    
    printf("Using custom configuration:\n");
    int result2 = identify_protocol_ex(buffer, data_len, &custom_config);
    printf("  Result: %s\n", get_protocol_name(result2));
    
    /* 测试边界情况 */
    HSM_REQ_HEAD header;
    header.tlen = htonl(sizeof(HSM_REQ_HEAD));
    header.checksum = htonl(0);
    header.action = htons(500);  /* 低于自定义配置的最小值 */
    header.version = htons(0x0100);
    header.reserve = htonl(0);
    
    printf("\nTesting action value 500 (below custom min 1000):\n");
    result1 = identify_protocol((unsigned char*)&header, sizeof(header));
    printf("  Default config result: %s\n", get_protocol_name(result1));
    
    result2 = identify_protocol_ex((unsigned char*)&header, sizeof(header), 
                                  &custom_config);
    printf("  Custom config result: %s\n", get_protocol_name(result2));
}

/**
 * @brief 主函数
 */
int main() {
    printf("Protocol Identifier Usage Example\n");
    printf("==================================\n");
    
    /* 演示基本的协议识别功能 */
    simulate_network_processing();
    
    /* 演示配置参数的使用 */
    demonstrate_configuration();
    
    printf("\n=== Example completed successfully ===\n");
    return 0;
}
