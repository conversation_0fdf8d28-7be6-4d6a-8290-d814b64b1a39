# VM3 (主节点) - /etc/keepalived/keepalived.conf
vrrp_instance VI_1 {
    state MASTER                    # 初始状态：主节点
    interface eth0.100              # 监听的网络接口
    virtual_router_id 51           # 虚拟路由器ID（关键配置）
    priority 150                   # 优先级：数值越高优先级越高
    advert_int 1                   # 心跳发送间隔：1秒
    authentication {
        auth_type PASS             # 认证类型：简单密码
        auth_pass mypassword123    # 认证密码
    }
    virtual_ipaddress {
        ***************           # 虚拟IP地址（VIP）
    }
    track_script {
        chk_service               # 服务检查脚本
    }
}

# VM4 (备节点) - /etc/keepalived/keepalived.conf  
vrrp_instance VI_1 {
    state BACKUP                   # 初始状态：备节点
    interface eth0.100             # 监听的网络接口
    virtual_router_id 51          # 必须与主节点相同！
    priority 100                  # 优先级：低于主节点
    advert_int 1                  # 心跳发送间隔：1秒
    authentication {
        auth_type PASS            # 认证类型：必须与主节点相同
        auth_pass mypassword123   # 认证密码：必须与主节点相同
    }
    virtual_ipaddress {
        ***************          # 虚拟IP地址：必须与主节点相同
    }
    track_script {
        chk_service              # 服务检查脚本
    }
}

# 服务检查脚本配置
vrrp_script chk_service {
    script "/usr/local/bin/check_service.sh"  # 检查脚本路径
    interval 2                                 # 检查间隔：2秒
    weight -20                                # 失败时优先级减少20
    fall 3                                    # 连续失败3次才认为故障
    rise 2                                    # 连续成功2次才认为恢复
}
