<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OVS VLAN 集群网络架构 - 分布式虚拟化解决方案</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; line-height: 1.6; background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%); }
        .container { max-width: 1400px; margin: 0 auto; background: white; padding: 30px; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .header h1 { color: #2c3e50; margin-bottom: 10px; font-size: 2.5em; }
        .header .subtitle { color: #7f8c8d; font-size: 1.2em; }
        .section { margin: 25px 0; padding: 20px; border-left: 5px solid #3498db; background: #f8f9fa; border-radius: 8px; }
        .highlight { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px; border-radius: 10px; margin: 15px 0; }
        .feature-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 20px; margin: 20px 0; }
        .feature-card { background: white; padding: 20px; border-radius: 12px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); border-top: 4px solid #3498db; }
        .feature-card h3 { color: #2c3e50; margin-bottom: 15px; }
        .btn { display: inline-block; padding: 15px 30px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; text-decoration: none; border-radius: 25px; margin: 10px 10px 10px 0; transition: transform 0.3s; }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 5px 15px rgba(0,0,0,0.2); }
        .comparison-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        .comparison-table th, .comparison-table td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        .comparison-table th { background: #3498db; color: white; }
        .comparison-table tr:nth-child(even) { background: #f2f2f2; }
        .tech-stack { background: #ecf0f1; padding: 15px; border-radius: 8px; margin: 10px 0; }
        .code-block { background: #2c3e50; color: #ecf0f1; padding: 15px; border-radius: 8px; font-family: 'Courier New', monospace; overflow-x: auto; }
        .architecture-flow { display: flex; justify-content: space-between; align-items: center; margin: 20px 0; flex-wrap: wrap; }
        .flow-item { background: #3498db; color: white; padding: 10px 20px; border-radius: 20px; margin: 5px; }
        .arrow { font-size: 1.5em; color: #3498db; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 OVS VLAN 集群网络架构</h1>
            <div class="subtitle">分布式虚拟化网络解决方案 - 企业级容器化平台</div>
        </div>
        
        <div class="highlight">
            <h2>🚀 集群架构核心特性</h2>
            <div class="architecture-flow">
                <div class="flow-item">5节点分布式集群</div>
                <span class="arrow">→</span>
                <div class="flow-item">负载均衡</div>
                <span class="arrow">→</span>
                <div class="flow-item">服务发现</div>
                <span class="arrow">→</span>
                <div class="flow-item">自动扩缩容</div>
                <span class="arrow">→</span>
                <div class="flow-item">高可用性</div>
            </div>
        </div>

        <div class="section">
            <h2>📊 快速访问</h2>
            <a href="https://app.diagrams.net/?lightbox=1&highlight=0000ff&edit=_blank&layers=1&nav=1&title=OVS_VLAN_Cluster_Network_Architecture.drawio#R" target="_blank" class="btn">
                🎯 在 Draw.io 中打开集群架构图
            </a>
            <a href="OVS_VLAN_Cluster_Network_Architecture.drawio" download class="btn">
                💾 下载架构图文件
            </a>
            <a href="test_drawio.html" class="btn">
                🔄 查看主备模式对比
            </a>
        </div>

        <div class="section">
            <h2>🏗️ 集群架构详细设计</h2>
            <div class="feature-grid">
                <div class="feature-card">
                    <h3>🔧 Trunk模式网络拓扑</h3>
                    <ul>
                        <li><strong>核心交换机集群</strong> - LACP链路聚合</li>
                        <li><strong>多VLAN支持</strong> - 100,200,300,400</li>
                        <li><strong>OVS网桥配置</strong> - Trunk端口设置</li>
                        <li><strong>VM多接口</strong> - eth0.100, eth0.200等</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>🔌 Access模式网络拓扑</h3>
                    <ul>
                        <li><strong>接入交换机</strong> - 按功能分VLAN</li>
                        <li><strong>业务网络</strong> - VLAN 100</li>
                        <li><strong>管理网络</strong> - VLAN 200</li>
                        <li><strong>存储网络</strong> - VLAN 300</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>☸️ Kubernetes集群管理</h3>
                    <ul>
                        <li><strong>Master节点</strong> - 控制平面组件</li>
                        <li><strong>Worker节点</strong> - 应用负载运行</li>
                        <li><strong>CNI网络</strong> - Flannel/Calico</li>
                        <li><strong>服务发现</strong> - etcd集群</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>⚖️ 负载均衡与入口控制</h3>
                    <ul>
                        <li><strong>Ingress Controller</strong> - Nginx/Traefik</li>
                        <li><strong>Service LoadBalancer</strong> - MetalLB</li>
                        <li><strong>健康检查</strong> - HTTP/TCP探测</li>
                        <li><strong>SSL终止</strong> - 证书管理</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📈 架构对比分析</h2>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>特性维度</th>
                        <th>主备模式 (VRRP)</th>
                        <th>集群模式 (Kubernetes)</th>
                        <th>优势对比</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>节点规模</strong></td>
                        <td>2个节点 (1主1备)</td>
                        <td>3-N个节点 (可扩展)</td>
                        <td>集群模式支持大规模部署</td>
                    </tr>
                    <tr>
                        <td><strong>故障恢复</strong></td>
                        <td>主备切换 (~8秒)</td>
                        <td>分布式高可用 (&lt;1秒)</td>
                        <td>集群模式恢复更快</td>
                    </tr>
                    <tr>
                        <td><strong>负载处理</strong></td>
                        <td>主节点承担全部负载</td>
                        <td>多节点负载均衡</td>
                        <td>集群模式性能更优</td>
                    </tr>
                    <tr>
                        <td><strong>扩展能力</strong></td>
                        <td>垂直扩展为主</td>
                        <td>水平扩展优先</td>
                        <td>集群模式扩展性更强</td>
                    </tr>
                    <tr>
                        <td><strong>运维复杂度</strong></td>
                        <td>配置简单，易维护</td>
                        <td>需要容器编排技能</td>
                        <td>主备模式更易上手</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="section">
            <h2>🛠️ 技术实现栈</h2>
            <div class="feature-grid">
                <div class="tech-stack">
                    <h3>🔧 基础设施层</h3>
                    <ul>
                        <li>Open vSwitch (OVS) - 虚拟交换机</li>
                        <li>VLAN 网络隔离 - 多租户支持</li>
                        <li>DPDK 网络加速 - 高性能数据平面</li>
                        <li>SR-IOV 硬件加速 - 网卡虚拟化</li>
                    </ul>
                </div>
                
                <div class="tech-stack">
                    <h3>☸️ 容器编排层</h3>
                    <ul>
                        <li>Kubernetes - 容器编排平台</li>
                        <li>Docker/Containerd - 容器运行时</li>
                        <li>CNI 网络插件 - Flannel/Calico</li>
                        <li>CSI 存储插件 - Ceph/GlusterFS</li>
                    </ul>
                </div>
                
                <div class="tech-stack">
                    <h3>🌐 服务网格层</h3>
                    <ul>
                        <li>Istio/Linkerd - 服务网格</li>
                        <li>Envoy Proxy - 数据平面代理</li>
                        <li>Jaeger - 分布式链路追踪</li>
                        <li>Prometheus - 监控指标收集</li>
                    </ul>
                </div>
                
                <div class="tech-stack">
                    <h3>📊 监控运维层</h3>
                    <ul>
                        <li>Grafana - 可视化面板</li>
                        <li>AlertManager - 告警管理</li>
                        <li>ELK Stack - 日志聚合分析</li>
                        <li>Helm - 应用包管理</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🚀 快速部署指南</h2>
            <h3>1. 集群初始化脚本</h3>
            <div class="code-block">
#!/bin/bash
# OVS VLAN 集群自动化部署

# 安装基础组件
curl -fsSL https://get.docker.com | sh
curl -s https://packages.cloud.google.com/apt/doc/apt-key.gpg | apt-key add -

# 配置OVS网桥
ovs-vsctl add-br br0
ovs-vsctl add-port br0 eth0
ovs-vsctl set port eth0 trunk=100,200,300,400

# 初始化Kubernetes集群
kubeadm init --pod-network-cidr=10.244.0.0/16
kubectl apply -f https://raw.githubusercontent.com/coreos/flannel/master/Documentation/kube-flannel.yml
            </div>
            
            <h3>2. 网络性能优化</h3>
            <div class="code-block">
# 启用DPDK加速
ovs-vsctl set Open_vSwitch . other_config:dpdk-init=true
ovs-vsctl set bridge br0 datapath_type=netdev

# 配置CPU亲和性
ovs-vsctl set Open_vSwitch . other_config:pmd-cpu-mask=0x6

# 网卡多队列配置
ethtool -L eth0 combined 4
            </div>
        </div>

        <div class="section">
            <h2>📋 适用场景分析</h2>
            <div class="feature-grid">
                <div class="feature-card">
                    <h3>🎯 集群模式适用场景</h3>
                    <ul>
                        <li>微服务架构应用</li>
                        <li>容器化云原生应用</li>
                        <li>需要弹性扩缩容的业务</li>
                        <li>DevOps CI/CD 环境</li>
                        <li>多租户SaaS平台</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <h3>⚠️ 主备模式适用场景</h3>
                    <ul>
                        <li>传统单体应用</li>
                        <li>数据库高可用</li>
                        <li>有状态服务</li>
                        <li>简单的Web应用</li>
                        <li>资源有限的环境</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>🔍 技术支持与资源</h2>
            <p>本架构图提供了完整的OVS VLAN集群网络解决方案，包含从基础网络配置到高级容器编排的全栈技术实现。</p>
            <ul>
                <li>📚 <strong>官方文档</strong>: Kubernetes、OVS、Docker官方文档</li>
                <li>🛠️ <strong>配置模板</strong>: 提供完整的YAML配置文件</li>
                <li>📊 <strong>监控方案</strong>: Prometheus + Grafana 监控栈</li>
                <li>🔧 <strong>故障排查</strong>: 详细的诊断命令和解决方案</li>
            </ul>
        </div>
    </div>
</body>
</html>
